import Foundation
import FirebaseFirestore

class ChatPreviewViewModel: ObservableObject {
    @Published var chatPreviews: [ChatPreview] = []
    private let db = Firestore.firestore()
    
    init() {
        print("ChatPreviewViewModel initialized")
        fetchChatPreviews()
    }
    
    func fetchChatPreviews() {
        print("Starting to fetch chat previews")
        db.collection("chatPreviews")
            .order(by: "timestamp", descending: true)
            .addSnapshotListener { [weak self] querySnapshot, error in
                guard let self = self else { return }
                
                if let error = error {
                    print("Error fetching documents: \(error.localizedDescription)")
                    return
                }
                
                guard let documents = querySnapshot?.documents else {
                    print("No documents found in chatPreviews collection")
                    return
                }
                
                print("Found \(documents.count) chat previews")
                
                self.chatPreviews = documents.compactMap { document in
                    do {
                        print("Processing document ID: \(document.documentID)")
                        let data = document.data()
                        print("Document data: \(data)")
                        
                        var chat = try document.data(as: ChatPreview.self)
                        chat.id = document.documentID
                        print("Successfully decoded chat: \(chat.title)")
                        return chat
                    } catch {
                        print("Error decoding chat preview: \(error)")
                        return nil
                    }
                }
                
                print("Final chat previews count: \(self.chatPreviews.count)")
            }
    }
} 
