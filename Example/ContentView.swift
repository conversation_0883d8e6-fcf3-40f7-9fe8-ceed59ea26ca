//
//  ContentView.swift
//  Text_Drama
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/25.
//

import SwiftUI
import FirebaseFirestore

struct ContentView: View {
    @StateObject private var viewModel = ChatPreviewViewModel()

    var body: some View {
        NavigationView {
            Group {
                if viewModel.chatPreviews.isEmpty {
                    VStack {
                        Text("Loading chats...")
                            .font(.headline)
                        ProgressView()
                    }
                } else {
                    List(viewModel.chatPreviews) { chat in
                        NavigationLink(destination: ChatDetailView(chatPreview: chat)) {
                            VStack(alignment: .leading, spacing: 8) {
                                Text(chat.title)
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text(chat.description)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .lineLimit(2)

                                HStack {
                                    Image(systemName: "message")
                                        .foregroundColor(.blue)
                                    Text("\(chat.messageCount) messages")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    Spacer()
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
            }
            .navigationTitle("Text Drama")
            .onAppear {
                print("ContentView appeared")
            }
        }
    }
}

#Preview {
    ContentView()
}
