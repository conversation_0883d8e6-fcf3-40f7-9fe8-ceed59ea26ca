import SwiftUI
import FirebaseFirestore

struct MessageBubble: View {
    let message: Message
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        HStack {
            if message.sender.lowercased().contains("boyfriend") ||
               message.sender.lowercased().contains("husband") ||
               message.sender.lowercased().contains("roommate2") ||
               message.sender.lowercased().contains("groom") {
                Spacer()
                VStack(alignment: .trailing, spacing: 1) {
                    Text(message.text)
                        .padding(10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))

                }
            } else {
                VStack(alignment: .leading, spacing: 1) {
                    Text(message.text)
                        .padding(10)
                        .background(Color(.systemGray5))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))

                }
                Spacer()
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 1)
    }
}

struct ChatDetailView: View {
    let chatPreview: ChatPreview
    @StateObject private var viewModel: ChatDetailViewModel

    init(chatPreview: ChatPreview) {
        self.chatPreview = chatPreview
        _viewModel = StateObject(wrappedValue: ChatDetailViewModel(chatId: chatPreview.id ?? ""))
    }

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(viewModel.messages) { message in
                    MessageBubble(message: message)
                }
            }
        }
        .navigationTitle(chatPreview.title)
        .navigationBarTitleDisplayMode(.inline)
    }
}