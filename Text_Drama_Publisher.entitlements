<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>com.apple.security.app-sandbox</key>
	<false/>
	<key>com.apple.security.files.user-selected.read-only</key>
	<true/>
	<key>com.apple.security.files.user-selected.read-write</key>
	<true/>
	<key>com.apple.security.network.client</key>
	<true/>
	<key>com.apple.security.keychain-access-groups</key>
	<array>
		<string>$(AppIdentifierPrefix)risul.rashed.com.Text-Drama-Publisher</string>
		<string>$(AppIdentifierPrefix)com.firebase.FiRInstallations</string>
		<string>$(AppIdentifierPrefix)com.firebase.auth</string>
		<string>$(AppIdentifierPrefix)com.googleusercontent.apps.847567038073-bb7e9c895603361063d6a8</string>
	</array>
	<key>com.apple.security.temporary-exception.shared-preference.read-write</key>
	<array>
		<string>com.firebase.auth</string>
		<string>com.firebase.FiRInstallations</string>
	</array>
</dict>
</plist>
