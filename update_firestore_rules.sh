#!/bin/bash

# This script updates the Firestore security rules to allow read/write access

# Define the Firebase project ID
# You may need to update this with your actual project ID
PROJECT_ID="text-drama"

echo "🔥 Firebase Security Rules Update Script"
echo "========================================"
echo "This will update your Firestore rules to allow read/write access."
echo "Project ID: $PROJECT_ID"
echo ""

# Create a temporary rules file
RULES_FILE="firestore.rules"

# Write the rules to the temporary file
cat > $RULES_FILE << 'EOL'
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      // Allow read/write access to all users
      allow read, write: if true;
    }
  }
}
EOL

echo "Created temporary rules file with the following content:"
cat $RULES_FILE

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "Firebase CLI is not installed. Please install it using:"
    echo "npm install -g firebase-tools"
    echo "Then run 'firebase login' to authenticate."
    echo "Alternatively, you can update the rules manually in the Firebase console:"
    echo "1. Go to https://console.firebase.google.com/project/$PROJECT_ID/firestore/rules"
    echo "2. Replace the rules with the content of $RULES_FILE"
    echo "3. Click 'Publish'"
    exit 1
fi

# Ask for confirmation before deploying
echo ""
echo "Do you want to deploy these rules to your Firebase project? (y/n)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    # Deploy the rules
    firebase deploy --only firestore:rules --project $PROJECT_ID

    # Clean up
    rm $RULES_FILE

    echo "Rules deployed successfully!"
    echo "Your Firestore database now allows read/write access to all users."
    echo "WARNING: This is not recommended for production use. Make sure to update the rules with proper authentication once your app is working."
else
    echo "Deployment cancelled. You can manually update the rules in the Firebase console:"
    echo "1. Go to https://console.firebase.google.com/project/$PROJECT_ID/firestore/rules"
    echo "2. Replace the rules with the content of $RULES_FILE"
    echo "3. Click 'Publish'"
fi
