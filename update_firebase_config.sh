#!/bin/bash

# This script updates the bundle ID in GoogleService-Info.plist to match your app's bundle ID

# Define the paths
PLIST_PATH="GoogleService-Info.plist"
NEW_BUNDLE_ID="risul.rashed.com.Text-Drama-Publisher"

# Check if the plist file exists
if [ ! -f "$PLIST_PATH" ]; then
    echo "Error: $PLIST_PATH not found!"
    exit 1
fi

# Make a backup of the original file
cp "$PLIST_PATH" "${PLIST_PATH}.backup"
echo "Created backup at ${PLIST_PATH}.backup"

# Update the bundle ID in the plist file
/usr/libexec/PlistBuddy -c "Set :BUNDLE_ID $NEW_BUNDLE_ID" "$PLIST_PATH"

echo "Updated bundle ID in $PLIST_PATH to $NEW_BUNDLE_ID"
echo "Done!"
