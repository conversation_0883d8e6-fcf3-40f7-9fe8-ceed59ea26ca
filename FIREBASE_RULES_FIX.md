# FIREBASE RULES FIX - Solve Permission Error

## The Problem
You're getting "❌ Failed to save chat preview: Missing or insufficient permissions."

## The Root Cause
Your Firebase Firestore rules are blocking write operations. The app tries to save to the `chatPreviews` collection but doesn't have permission.

## The Solution
Update your Firebase rules to allow read/write access.

## STEP 1: Go to Firebase Console
1. Open https://console.firebase.google.com/
2. Select your **text-drama** project
3. Click **Firestore Database** in the left sidebar
4. Click the **Rules** tab at the top

## STEP 2: Replace Rules with This Code

**Copy and paste this EXACTLY:**

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

## STEP 3: Publish the Rules
1. Click the **Publish** button
2. Wait for "Rules published successfully" message

## STEP 4: Test Your App
1. Restart your app completely
2. Try generating and saving a new chat
3. The permission error should be gone

## What I Fixed in the Code
✅ Added authentication check before saving to Firebase
✅ Added automatic anonymous sign-in if user not authenticated  
✅ Added proper error handling for authentication failures
✅ Added FirebaseAuth import to AIGenerationView

## Why This Works
- The rules above allow anyone to read/write your database (good for development)
- The code now ensures users are authenticated before saving
- Anonymous authentication happens automatically in the background

## Alternative: More Secure Rules (Optional)
If you want some security but still simple, use these rules instead:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

**If you use the secure rules, also enable Anonymous Authentication:**
1. Go to Firebase Console > Authentication > Sign-in method
2. Find "Anonymous" and toggle it ON
3. Click Save

## Recommendation
Use the first rules (allow if true) for now since they're guaranteed to work. You can add security later once everything is working perfectly.

After applying these rules, your app should save chats without any permission errors!
