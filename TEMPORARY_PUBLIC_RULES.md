# TEMPORARY: Public Write Rules (Until Auth is Fixed)

## 🚨 Immediate Solution

Since authentication is failing, use these TEMPORARY rules to allow you to work:

### Firestore Rules (Temporary - Public Write)

Go to **Firebase Console → Firestore Database → Rules**:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow public read and write
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

### Storage Rules (Temporary - Public Write)

Go to **Firebase Console → Storage → Rules**:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // TEMPORARY: Allow public read and write
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## ⚠️ IMPORTANT

These rules are **TEMPORARY** and allow anyone to read/write your database.

**Use these rules ONLY until authentication is working properly.**

## Steps to Apply:

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select "text-drama" project**
3. **Apply Firestore rules** (above)
4. **Apply Storage rules** (above)
5. **Enable Authentication methods**:
   - Authentication → Sign-in method
   - Enable "Email/Password"
   - Enable "Anonymous"
6. **Test your app** - you should be able to save content now

## After Authentication Works:

Once authentication is working properly, switch back to the secure rules in `READ_PUBLIC_WRITE_AUTH_RULES.md`.

This is just a temporary workaround to let you continue working!
