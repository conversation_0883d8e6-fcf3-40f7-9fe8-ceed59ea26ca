// Production Firestore Security Rules for Text Drama Publisher
// Copy this content to Firebase Console → Firestore Database → Rules

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // OPTION 1: Public Read, Authenticated Write (Recommended for Production)
    // Requires Firebase Authentication to be working
    match /{document=**} {
      allow read: if true;  // Anyone can read content
      allow write: if request.auth != null;  // Only authenticated users can write
    }
    
    // OPTION 2: Completely Open (Development Only - NOT for Production)
    // Uncomment this and comment out Option 1 if you need open access
    /*
    match /{document=**} {
      allow read, write: if true;
    }
    */
    
    // OPTION 3: More Granular Rules (Advanced Production Setup)
    // Uncomment this section and comment out Option 1 for more control
    /*
    // Public read access for content
    match /chatPreviews/{chatId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    match /dramas/{dramaId} {
      allow read: if true;
      allow write: if request.auth != null;
      
      match /messages/{messageId} {
        allow read: if true;
        allow write: if request.auth != null;
      }
    }
    
    // User-specific data (only the user can access their own data)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin-only collections
    match /admin/{document=**} {
      allow read, write: if request.auth != null && 
                           request.auth.token.admin == true;
    }
    */
  }
}

// Instructions:
// 1. Go to Firebase Console: https://console.firebase.google.com/
// 2. Select your project
// 3. Go to Firestore Database → Rules
// 4. Replace the existing rules with one of the options above
// 5. Click "Publish"
//
// For immediate testing: Use Option 2 (completely open)
// For production: Use Option 1 (authenticated write) or Option 3 (granular)
