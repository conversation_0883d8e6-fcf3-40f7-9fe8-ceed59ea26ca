//
//  GeminiService.swift
//  Text_Drama_Publisher
//
//  Created by Augment on 5/16/25.
//

import Foundation
import Combine

class GeminiService: ObservableObject {
    @Published var isLoading = false
    @Published var error: String? = nil

    private let apiKey: String
    // Updated to use Gemini 2.0 Flash model
    private let baseURL = "https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent"

    // Alternative endpoint format that might work better in some environments
    private var fullURL: String {
        return "\(baseURL)?key=\(apiKey)"
    }

    init(apiKey: String) {
        self.apiKey = apiKey
        print("GeminiService initialized with API key: \(apiKey.prefix(5))...[REDACTED]")
    }

    // Private cancellables for test connection
    private var testConnectionCancellables = Set<AnyCancellable>()

    // Simple test function to verify API connectivity
    func testConnection() -> AnyPublisher<Bool, Error> {
        let publisher = PassthroughSubject<Bool, Error>()

        // Clear previous cancellables
        testConnectionCancellables.removeAll()

        let testPrompt = "Hello, this is a test request. Please respond with a short greeting."

        // Try a simpler direct HTTP request for testing with Gemini 2.0 Flash model
        let urlString = "https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent?key=\(apiKey)"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Invalid URL for test"])
            publisher.send(completion: .failure(error))
            return publisher.eraseToAnyPublisher()
        }

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        [
                            "text": testPrompt
                        ]
                    ]
                ]
            ]
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Failed to serialize test request"])
            publisher.send(completion: .failure(error))
            return publisher.eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData

        print("🧪 Testing API connection with URL: \(urlString)")

        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ API test failed with network error: \(error.localizedDescription)")
                    publisher.send(completion: .failure(error))
                    return
                }

                if let httpResponse = response as? HTTPURLResponse {
                    print("📡 Test response status code: \(httpResponse.statusCode)")

                    if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                        print("✅ API test successful")
                        publisher.send(true)
                        publisher.send(completion: .finished)
                        return
                    } else {
                        // Try to extract error message from response
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            print("📄 Error response: \(responseString)")
                            let error = NSError(domain: "GeminiService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API returned status code \(httpResponse.statusCode)"])
                            publisher.send(completion: .failure(error))
                        } else {
                            let error = NSError(domain: "GeminiService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API returned status code \(httpResponse.statusCode)"])
                            publisher.send(completion: .failure(error))
                        }
                        return
                    }
                }

                // If we get here, something unexpected happened
                let error = NSError(domain: "GeminiService", code: 500, userInfo: [NSLocalizedDescriptionKey: "Unexpected response from API test"])
                publisher.send(completion: .failure(error))
            }
        }.resume()

        return publisher.eraseToAnyPublisher()
    }

    func generateTextDrama(prompt: String) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        isLoading = true
        error = nil

        // Use a direct URL string construction which might be more reliable
        let urlString = "https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent?key=\(apiKey)"
        guard let url = URL(string: urlString) else {
            let errorMsg = "Invalid URL construction"
            print("❌ \(errorMsg): \(urlString)")
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = errorMsg
            return publisher.eraseToAnyPublisher()
        }

        // Create the request body according to Gemini API specifications
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        [
                            "text": prompt
                        ]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096,
                "stopSequences": []
            ]
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Failed to serialize request body"])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = error.localizedDescription
            return publisher.eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 60.0 // Set 60 second timeout

        print("🚀 Sending request to Gemini API with URL: \(url.absoluteString)")
        print("📝 Request body: \(String(data: jsonData, encoding: .utf8) ?? "Unable to print request body")")
        print("⏱️ Request timeout set to \(request.timeoutInterval) seconds")

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false

                if let httpResponse = response as? HTTPURLResponse {
                    print("📡 Received response with status code: \(httpResponse.statusCode)")
                }

                if let error = error {
                    print("❌ Network error: \(error.localizedDescription)")
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                guard let data = data else {
                    let errorMsg = "No data received from API"
                    print("❌ \(errorMsg)")
                    let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                // Print raw response for debugging
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📥 Raw API response: \(responseString)")
                }

                do {
                    let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]

                    // Check for error in response
                    if let errorInfo = json?["error"] as? [String: Any],
                       let message = errorInfo["message"] as? String {
                        print("❌ API error: \(message)")
                        let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: message])
                        self.error = error.localizedDescription
                        publisher.send(completion: .failure(error))
                        return
                    }

                    // Debug: Print the full response structure
                    print("🔍 Full API Response JSON:")
                    if let jsonData = try? JSONSerialization.data(withJSONObject: json ?? [:], options: .prettyPrinted),
                       let jsonString = String(data: jsonData, encoding: .utf8) {
                        print(jsonString)
                    }

                    // Check for candidates
                    if let candidates = json?["candidates"] as? [[String: Any]],
                       let firstCandidate = candidates.first,
                       let content = firstCandidate["content"] as? [String: Any],
                       let parts = content["parts"] as? [[String: Any]],
                       let firstPart = parts.first,
                       let text = firstPart["text"] as? String {

                        print("✅ Successfully received text from Gemini API")
                        print("📄 Text length: \(text.count)")
                        print("📄 First few characters: \(String(text.prefix(100)))...")

                        publisher.send(text)
                        publisher.send(completion: .finished)
                    } else {
                        print("❌ Failed to parse response structure")
                        print("📊 Available keys in response: \(json?.keys.sorted() ?? [])")

                        // Check if there's an error in the response
                        if let error = json?["error"] as? [String: Any] {
                            print("🚨 API Error: \(error)")
                            let errorMessage = error["message"] as? String ?? "Unknown API error"
                            let apiError = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "API Error: \(errorMessage)"])
                            self.error = "API Error: \(errorMessage)"
                            publisher.send(completion: .failure(apiError))
                        } else {
                            let errorMsg = "Failed to parse response from Gemini API - unexpected structure"
                            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                            self.error = errorMsg
                            publisher.send(completion: .failure(error))
                        }
                    }
                } catch {
                    print("❌ JSON parsing error: \(error.localizedDescription)")
                    self.error = "Failed to parse JSON response: \(error.localizedDescription)"
                    publisher.send(completion: .failure(error))
                }
            }
        }.resume()

        return publisher.eraseToAnyPublisher()
    }

    // Private cancellables for internal subscription management
    private var dramaGenerationCancellables = Set<AnyCancellable>()

    // Helper method to generate a text drama with specific parameters
    func generateDrama(title: String? = nil, characters: [String]? = nil, setting: String? = nil, style: String? = nil, startingPoint: String? = nil, endingPoint: String? = nil, additionalPrompt: String? = nil, messageCount: Int = 20) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        // Clear previous cancellables
        dramaGenerationCancellables.removeAll()

        var prompt = "Generate an extremely realistic text message conversation between characters that feels completely authentic and human. "

        if let title = title {
            prompt += "The title should be '\(title)'. "
        }

        if let characters = characters, !characters.isEmpty {
            prompt += "The characters are: \(characters.joined(separator: ", ")). "
        } else {
            prompt += "Create appropriate characters for the drama. "
        }

        if let setting = setting {
            prompt += "The setting is: \(setting). "
        }

        if let style = style {
            prompt += "The style should be: \(style). "
        }

        if let startingPoint = startingPoint {
            prompt += "The conversation should start with: '\(startingPoint)'. "
        }

        if let endingPoint = endingPoint {
            prompt += "The conversation should end around: '\(endingPoint)' (this will be used as a continuation point for future conversations). "
        }

        if let additionalPrompt = additionalPrompt {
            prompt += "Additional instructions: \(additionalPrompt). "
        }

        // Add context about modern texting behavior
        prompt += """
        Remember that this should read exactly like real text messages between actual people.

        Consider these aspects of modern texting:
        - People often send multiple short messages in a row instead of one long message
        - Messages might include "..." when someone is thinking or typing more
        - People use abbreviations like "lol", "omg", "btw", "idk", etc.
        - Include realistic reactions to messages (like "haha", "lmao", "wow", etc.)
        - People sometimes take time to respond or change topics abruptly
        - Include some messages that are just emoji reactions
        - Sometimes people make typos and either correct them or ignore them
        - Include occasional message timestamps if it helps show time passing
        """

        prompt += """
        Format the output EXACTLY as a series of text messages with each message on a new line, with the sender's name followed by a colon, then the message. For example:

        John: hey what's up
        Sarah: not much, just got home from work
        Sarah: exhausted lol
        John: same 😩
        John: this week has been crazy
        John: wanna grab dinner later?
        Sarah: sure!
        Sarah: where were u thinking?
        John: idk, maybe that new place downtown?
        Sarah: the italian one?
        John: yeah
        Sarah: sounds good! 7pm?

        Make the conversation EXTREMELY realistic and natural, as if it's a genuine text exchange between real people. Include approximately \(messageCount) messages with a clear beginning, middle, and end to the conversation.

        Important guidelines for realism:
        - Use casual language, contractions, abbreviations, and occasional typos like real people do
        - Include short replies, one-word responses, and emoji where appropriate
        - Add natural pauses or topic changes in the conversation flow
        - Include occasional references to time passing ("sorry for the late reply")
        - Make characters interrupt each other or ask follow-up questions
        - Include some messages that are reactions to previous messages
        - Avoid overly formal language or perfect grammar in every message
        - Make each character have a distinct personality and texting style
        - Include occasional slang, humor, and personality-specific phrases
        - Don't make every message perfectly balanced in length - some should be very short, others longer

        Ensure there's a good back-and-forth dialogue between the characters with an approximately equal number of messages from each character. Don't introduce new characters halfway through the conversation - establish all characters at the beginning.

        MOST IMPORTANT: Avoid any patterns that make the conversation feel AI-generated or scripted. The conversation should feel completely natural and spontaneous, with the imperfections and quirks of real human texting. Avoid perfect grammar, overly formal language, or perfectly balanced exchanges. Make it messy, authentic, and genuinely human.
        """

        // Try with the primary model first
        generateTextDrama(prompt: prompt)
            .sink(
                receiveCompletion: { completion in
                    switch completion {
                    case .finished:
                        publisher.send(completion: .finished)
                    case .failure(let error):
                        print("❌ Primary model failed: \(error.localizedDescription)")
                        print("🔄 Trying fallback model...")

                        // If the primary model fails, try with a fallback model
                        self.generateWithFallbackModel(prompt: prompt)
                            .sink(
                                receiveCompletion: { fallbackCompletion in
                                    switch fallbackCompletion {
                                    case .finished:
                                        publisher.send(completion: .finished)
                                    case .failure(let fallbackError):
                                        print("❌ First fallback model failed: \(fallbackError.localizedDescription)")
                                        print("🔄 Trying second fallback model...")

                                        // If the first fallback model fails, try with a second fallback model
                                        self.generateWithSecondFallbackModel(prompt: prompt)
                                            .sink(
                                                receiveCompletion: { secondFallbackCompletion in
                                                    publisher.send(completion: secondFallbackCompletion)
                                                },
                                                receiveValue: { result in
                                                    publisher.send(result)
                                                }
                                            )
                                            .store(in: &self.dramaGenerationCancellables)
                                    }
                                },
                                receiveValue: { result in
                                    publisher.send(result)
                                }
                            )
                            .store(in: &self.dramaGenerationCancellables)
                    }
                },
                receiveValue: { result in
                    publisher.send(result)
                }
            )
            .store(in: &dramaGenerationCancellables)

        return publisher.eraseToAnyPublisher()
    }

    // Fallback method using a different model
    private func generateWithFallbackModel(prompt: String) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        isLoading = true
        error = nil

        // Use a different model for fallback
        let urlString = "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent?key=\(apiKey)"
        guard let url = URL(string: urlString) else {
            let errorMsg = "Invalid URL construction for fallback model"
            print("❌ \(errorMsg): \(urlString)")
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = errorMsg
            return publisher.eraseToAnyPublisher()
        }

        // Create the request body
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        [
                            "text": prompt
                        ]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096,
                "stopSequences": []
            ]
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Failed to serialize request body for fallback model"])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = error.localizedDescription
            return publisher.eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30

        print("🚀 Sending fallback request to Gemini API with URL: \(url.absoluteString)")

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false

                if let httpResponse = response as? HTTPURLResponse {
                    print("📡 Fallback response status code: \(httpResponse.statusCode)")
                }

                if let error = error {
                    print("❌ Fallback network error: \(error.localizedDescription)")
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                guard let data = data else {
                    let errorMsg = "No data received from fallback API"
                    print("❌ \(errorMsg)")
                    let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                // Print raw response for debugging
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📥 Fallback raw API response: \(responseString)")
                }

                do {
                    let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]

                    // Check for error in response
                    if let errorInfo = json?["error"] as? [String: Any],
                       let message = errorInfo["message"] as? String {
                        print("❌ Fallback API error: \(message)")
                        let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: message])
                        self.error = error.localizedDescription
                        publisher.send(completion: .failure(error))
                        return
                    }

                    // Check for candidates
                    if let candidates = json?["candidates"] as? [[String: Any]],
                       let firstCandidate = candidates.first,
                       let content = firstCandidate["content"] as? [String: Any],
                       let parts = content["parts"] as? [[String: Any]],
                       let firstPart = parts.first,
                       let text = firstPart["text"] as? String {

                        print("✅ Successfully received text from fallback Gemini API")
                        print("📄 First few characters: \(String(text.prefix(50)))...")

                        publisher.send(text)
                        publisher.send(completion: .finished)
                    } else {
                        print("❌ Failed to parse fallback response structure")
                        print("📊 JSON structure: \(json ?? [:])")

                        let errorMsg = "Failed to parse response from fallback Gemini API"
                        let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                        self.error = errorMsg
                        publisher.send(completion: .failure(error))
                    }
                } catch {
                    print("❌ Fallback JSON parsing error: \(error.localizedDescription)")
                    self.error = "Failed to parse JSON response from fallback: \(error.localizedDescription)"
                    publisher.send(completion: .failure(error))
                }
            }
        }.resume()

        return publisher.eraseToAnyPublisher()
    }

    // Second fallback method using yet another model
    private func generateWithSecondFallbackModel(prompt: String) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        isLoading = true
        error = nil

        // Use a different model for second fallback
        let urlString = "https://generativelanguage.googleapis.com/v1/models/gemini-1.0-pro:generateContent?key=\(apiKey)"
        guard let url = URL(string: urlString) else {
            let errorMsg = "Invalid URL construction for second fallback model"
            print("❌ \(errorMsg): \(urlString)")
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = errorMsg
            return publisher.eraseToAnyPublisher()
        }

        // Create the request body with simpler configuration
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        [
                            "text": prompt
                        ]
                    ]
                ]
            ]
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Failed to serialize request body for second fallback model"])
            publisher.send(completion: .failure(error))
            isLoading = false
            self.error = error.localizedDescription
            return publisher.eraseToAnyPublisher()
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30

        print("🚀 Sending second fallback request to Gemini API with URL: \(url.absoluteString)")

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false

                if let httpResponse = response as? HTTPURLResponse {
                    print("📡 Second fallback response status code: \(httpResponse.statusCode)")
                }

                if let error = error {
                    print("❌ Second fallback network error: \(error.localizedDescription)")
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                guard let data = data else {
                    let errorMsg = "No data received from second fallback API"
                    print("❌ \(errorMsg)")
                    let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                    self.error = error.localizedDescription
                    publisher.send(completion: .failure(error))
                    return
                }

                // Print raw response for debugging
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📥 Second fallback raw API response: \(responseString)")
                }

                do {
                    let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]

                    // Check for error in response
                    if let errorInfo = json?["error"] as? [String: Any],
                       let message = errorInfo["message"] as? String {
                        print("❌ Second fallback API error: \(message)")
                        let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: message])
                        self.error = error.localizedDescription
                        publisher.send(completion: .failure(error))
                        return
                    }

                    // Check for candidates
                    if let candidates = json?["candidates"] as? [[String: Any]],
                       let firstCandidate = candidates.first,
                       let content = firstCandidate["content"] as? [String: Any],
                       let parts = content["parts"] as? [[String: Any]],
                       let firstPart = parts.first,
                       let text = firstPart["text"] as? String {

                        print("✅ Successfully received text from second fallback Gemini API")
                        print("📄 First few characters: \(String(text.prefix(50)))...")

                        publisher.send(text)
                        publisher.send(completion: .finished)
                    } else {
                        print("❌ Failed to parse second fallback response structure")
                        print("📊 JSON structure: \(json ?? [:])")

                        let errorMsg = "Failed to parse response from second fallback Gemini API"
                        let error = NSError(domain: "GeminiService", code: 400, userInfo: [NSLocalizedDescriptionKey: errorMsg])
                        self.error = errorMsg
                        publisher.send(completion: .failure(error))
                    }
                } catch {
                    print("❌ Second fallback JSON parsing error: \(error.localizedDescription)")
                    self.error = "Failed to parse JSON response from second fallback: \(error.localizedDescription)"
                    publisher.send(completion: .failure(error))
                }
            }
        }.resume()

        return publisher.eraseToAnyPublisher()
    }
    // Generate a catchy description for a chat
    func generateCatchyDescription(title: String? = nil, style: String? = nil, setting: String? = nil, categories: [String]? = nil) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        // Build a prompt for generating a catchy description
        var prompt = "Generate a single catchy, engaging, and creative one-line description for a text message conversation. "
        prompt += "The description should be attention-grabbing, memorable, and make people want to read the conversation. "
        prompt += "Keep it under 100 characters. Don't use quotes. Don't include any explanations or additional text. "

        if let title = title, !title.isEmpty {
            prompt += "The conversation is titled '\(title)'. "
        }

        if let style = style, !style.isEmpty {
            prompt += "The style is '\(style)'. "
        }

        if let setting = setting, !setting.isEmpty {
            prompt += "The setting is '\(setting)'. "
        }

        if let categories = categories, !categories.isEmpty {
            prompt += "The categories are: \(categories.joined(separator: ", ")). "
        }

        // Use the existing generateTextDrama function which handles all the API calls
        generateTextDrama(prompt: prompt)
            .sink(
                receiveCompletion: { completion in
                    publisher.send(completion: completion)
                },
                receiveValue: { result in
                    // Clean up the result - remove any quotes, extra spaces, or newlines
                    let cleanedResult = result
                        .replacingOccurrences(of: "\"", with: "")
                        .replacingOccurrences(of: "\n", with: " ")
                        .trimmingCharacters(in: .whitespacesAndNewlines)

                    // If the result is too long, truncate it
                    let finalResult = cleanedResult.count > 100
                        ? cleanedResult.prefix(97) + "..."
                        : cleanedResult

                    publisher.send(finalResult)
                }
            )
            .store(in: &dramaGenerationCancellables)

        return publisher.eraseToAnyPublisher()
    }

}