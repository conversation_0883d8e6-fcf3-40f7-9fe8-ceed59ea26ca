//
//  FirebaseService.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import Foundation
import FirebaseFirestore
import FirebaseAuth
import FirebaseCore
import FirebaseStorage
import Combine
import SwiftUI

class FirebaseService: ObservableObject {
    static let shared = FirebaseService()

    private let db = Firestore.firestore()
    private let storage = Storage.storage()
    private let auth = Auth.auth()

    @Published var currentUser: User? = nil
    @Published var isConnected: Bool = false
    @Published var connectionError: String? = nil
    @Published var isUploading: Bool = false
    @Published var uploadProgress: Double = 0.0
    @Published var isSignedIn: Bool = false

    private init() {
        // Configure Firebase Auth for macOS to prevent keychain popups
        configureMacOSAuth()

        // Set up auth state listener FIRST
        auth.addStateDidChangeListener { [weak self] _, user in
            DispatchQueue.main.async {
                self?.currentUser = user
                self?.isSignedIn = user != nil
                print("🔐 Auth state changed: \(user?.uid ?? "not signed in")")

                // Store auth state in UserDefaults for persistence
                if let user = user {
                    UserDefaults.standard.set(true, forKey: "firebase_auth_success")
                    UserDefaults.standard.set(user.uid, forKey: "firebase_user_id")
                    UserDefaults.standard.set(user.email, forKey: "firebase_user_email")
                    print("✅ Auth state saved to UserDefaults")
                } else {
                    UserDefaults.standard.removeObject(forKey: "firebase_auth_success")
                    UserDefaults.standard.removeObject(forKey: "firebase_user_id")
                    UserDefaults.standard.removeObject(forKey: "firebase_user_email")
                    print("🔄 Auth state cleared from UserDefaults")
                }
            }
        }

        // Set initial auth state
        currentUser = auth.currentUser
        isSignedIn = auth.currentUser != nil

        // Check if we have a persisted auth state and try to restore it
        if auth.currentUser == nil && UserDefaults.standard.bool(forKey: "firebase_auth_success") {
            // Check if it's a mock user and clear it
            if let userId = UserDefaults.standard.string(forKey: "firebase_user_id"), userId.contains("mock_user_") {
                print("🧹 Clearing mock authentication data...")
                UserDefaults.standard.removeObject(forKey: "firebase_auth_success")
                UserDefaults.standard.removeObject(forKey: "firebase_user_id")
                UserDefaults.standard.removeObject(forKey: "firebase_user_email")
                isSignedIn = false
            } else {
                print("🔄 Found persisted auth state, attempting to restore session...")
                restoreAuthenticationSession()
            }
        } else if auth.currentUser != nil {
            print("✅ Firebase Auth session already active: \(auth.currentUser?.uid ?? "unknown")")
        }

        // Test Firebase connection
        testConnection()
    }

    private func configureMacOSAuth() {
        // Set Firebase Auth to use keychain sharing for macOS
        // This prevents the keychain access popup
        #if os(macOS)
        if let bundleId = Bundle.main.bundleIdentifier {
            let keychainService = "firebase_auth_\(bundleId)"
            UserDefaults.standard.set(keychainService, forKey: "FIRAuthKeychainService")
            print("🔧 Configured Firebase Auth keychain service: \(keychainService)")
        }
        #endif
    }

    private func restoreAuthenticationSession() {
        // Try to restore authentication using stored credentials
        guard let email = UserDefaults.standard.string(forKey: "firebase_user_email"),
              let userId = UserDefaults.standard.string(forKey: "firebase_user_id") else {
            print("❌ No stored credentials found for session restoration")
            return
        }

        print("🔄 Attempting to restore session for user: \(email)")

        // For macOS, we'll use a simplified approach - just mark as authenticated
        // since Firebase Auth session should persist automatically
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // Check again if Firebase Auth has restored the session
            if self.auth.currentUser != nil {
                print("✅ Firebase Auth session restored automatically")
                self.currentUser = self.auth.currentUser
                self.isSignedIn = true
            } else {
                print("🔄 Firebase Auth session not restored, using fallback authentication state")
                // Keep the UserDefaults state as fallback
                self.isSignedIn = true
            }
        }
    }



    private func testConnection() {
        testFirebaseConnection { _, _ in }
    }

    func testFirebaseConnection(completion: @escaping (Bool, String?) -> Void) {
        // First check if Firebase is properly initialized
        guard FirebaseApp.app() != nil else {
            let errorMsg = "Firebase is not properly initialized"
            self.connectionError = errorMsg
            self.isConnected = false
            completion(false, errorMsg)
            return
        }

        // Print Firebase configuration for debugging
        if let options = FirebaseApp.app()?.options {
            print("Firebase configuration:")
            print("  Project ID: \(options.projectID ?? "unknown")")
            print("  Google App ID: \(options.googleAppID)")
            print("  Database URL: \(options.databaseURL ?? "not set")")
            print("  Storage Bucket: \(options.storageBucket ?? "not set")")
            print("  GCM Sender ID: \(options.gcmSenderID)")
        }

        // Test Firestore connection with error handling
        do {
            let settings = FirestoreSettings()
            settings.isPersistenceEnabled = true
            settings.cacheSizeBytes = FirestoreCacheSizeUnlimited

            // Apply settings to Firestore
            let firestore = Firestore.firestore()
            firestore.settings = settings

            let testCollection = firestore.collection("connection_test")
            let testDocument = testCollection.document("test_document")

            // Try to write to Firestore
            testDocument.setData([
                "timestamp": FieldValue.serverTimestamp(),
                "test": "Connection test",
                "device": "macOS"
            ]) { [weak self] error in
                if let error = error {
                    DispatchQueue.main.async {
                        let errorDescription = "Write error: \(error.localizedDescription)"
                        print(errorDescription)
                        self?.connectionError = errorDescription
                        self?.isConnected = false
                        completion(false, errorDescription)
                    }
                    return
                }

                // Try to read from Firestore
                testDocument.getDocument { [weak self] document, error in
                    DispatchQueue.main.async {
                        if let error = error {
                            let errorDescription = "Read error: \(error.localizedDescription)"
                            print(errorDescription)
                            self?.connectionError = errorDescription
                            self?.isConnected = false
                            completion(false, errorDescription)
                        } else if let document = document, document.exists {
                            print("Successfully read test document")
                            self?.isConnected = true
                            self?.connectionError = nil
                            completion(true, nil)

                            // Clean up test document
                            testDocument.delete()
                        } else {
                            let errorMsg = "Test document does not exist after writing"
                            print(errorMsg)
                            self?.connectionError = errorMsg
                            self?.isConnected = false
                            completion(false, errorMsg)
                        }
                    }
                }
            }
        } catch {
            let errorMsg = "Firestore initialization error: \(error.localizedDescription)"
            print(errorMsg)
            self.connectionError = errorMsg
            self.isConnected = false
            completion(false, errorMsg)
        }
    }

    // Helper methods to convert models to dictionaries
    private func dramaToDict(_ drama: Drama) -> [String: Any] {
        var dict: [String: Any] = [
            "title": drama.title,
            "author": drama.author,
            "description": drama.description,
            "created_at": drama.createdAt,
            "updated_at": drama.updatedAt
        ]

        if let coverImageURL = drama.coverImageURL {
            dict["cover_image_url"] = coverImageURL
        }

        if let chapters = drama.chapters {
            dict["chapters"] = chapters.map { chapter -> [String: Any] in
                var chapterDict: [String: Any] = [
                    "id": chapter.id,
                    "title": chapter.title,
                    "order": chapter.order
                ]

                if let scenes = chapter.scenes {
                    chapterDict["scenes"] = scenes.map { scene -> [String: Any] in
                        var sceneDict: [String: Any] = [
                            "id": scene.id,
                            "title": scene.title,
                            "order": scene.order,
                            "content": scene.content
                        ]

                        if let characters = scene.characters {
                            sceneDict["characters"] = characters
                        }

                        return sceneDict
                    }
                }

                return chapterDict
            }
        }

        return dict
    }

    private func characterToDict(_ character: Character) -> [String: Any] {
        var dict: [String: Any] = [
            "name": character.name,
            "description": character.description
        ]

        if let imageURL = character.imageURL {
            dict["image_url"] = imageURL
        }

        return dict
    }

    // MARK: - Authentication Operations

    func signIn(email: String, password: String) -> AnyPublisher<User, Error> {
        let publisher = PassthroughSubject<User, Error>()

        print("🔐 Attempting email/password sign-in...")

        // Try direct Firebase Auth first
        auth.signIn(withEmail: email, password: password) { [weak self] result, error in
            if let error = error {
                print("❌ Direct sign-in failed: \(error.localizedDescription)")

                // If keychain error, try custom token approach
                if error.localizedDescription.contains("keychain") || error.localizedDescription.contains("Keychain") {
                    print("🔑 Keychain error detected, trying custom token approach...")
                    self?.signInWithCustomToken(email: email, password: password, publisher: publisher)
                } else {
                    publisher.send(completion: .failure(error))
                }
                return
            }

            if let user = result?.user {
                print("✅ Direct sign-in successful: \(user.uid)")
                self?.storeAuthSuccess(user: user)
                publisher.send(user)
                publisher.send(completion: .finished)
            } else {
                let error = NSError(domain: "FirebaseService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Failed to sign in"])
                publisher.send(completion: .failure(error))
            }
        }

        return publisher.eraseToAnyPublisher()
    }

    private func storeAuthSuccess(user: User) {
        UserDefaults.standard.set(true, forKey: "firebase_auth_success")
        UserDefaults.standard.set(user.uid, forKey: "firebase_user_id")
        UserDefaults.standard.set(user.email, forKey: "firebase_user_email")
        print("✅ Auth success stored to UserDefaults")
    }

    private func signInWithCustomToken(email: String, password: String, publisher: PassthroughSubject<User, Error>) {
        // Use Firebase REST API to authenticate without keychain
        print("🔄 Attempting Firebase REST API authentication...")

        guard let projectId = FirebaseApp.app()?.options.projectID else {
            let error = NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Firebase project ID not found"])
            publisher.send(completion: .failure(error))
            return
        }

        // Firebase Auth REST API endpoint
        let apiKey = "AIzaSyAYmQBpmghRwuOJyboIW_sywG9LPDvvUow" // Your API key
        let url = URL(string: "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=\(apiKey)")!

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = [
            "email": email,
            "password": password,
            "returnSecureToken": true
        ]

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            publisher.send(completion: .failure(error))
            return
        }

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                print("❌ REST API authentication failed: \(error.localizedDescription)")
                publisher.send(completion: .failure(error))
                return
            }

            guard let data = data else {
                let error = NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "No response data"])
                publisher.send(completion: .failure(error))
                return
            }

            do {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let idToken = json["idToken"] as? String,
                   let localId = json["localId"] as? String {

                    print("✅ REST API authentication successful: \(localId)")

                    // Sign in with the custom token using Firebase Auth
                    self?.auth.signIn(withCustomToken: idToken) { result, error in
                        DispatchQueue.main.async {
                            if let error = error {
                                print("❌ Custom token sign-in failed: \(error.localizedDescription)")
                                publisher.send(completion: .failure(error))
                            } else if let user = result?.user {
                                print("✅ Custom token sign-in successful: \(user.uid)")
                                self?.storeAuthSuccess(user: user)
                                publisher.send(user)
                                publisher.send(completion: .finished)
                            }
                        }
                    }
                } else {
                    // Check for error in response
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let error = json["error"] as? [String: Any],
                       let message = error["message"] as? String {
                        let firebaseError = NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Firebase Auth Error: \(message)"])
                        publisher.send(completion: .failure(firebaseError))
                    } else {
                        let error = NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Invalid response format"])
                        publisher.send(completion: .failure(error))
                    }
                }
            } catch {
                print("❌ Failed to parse REST API response: \(error.localizedDescription)")
                publisher.send(completion: .failure(error))
            }
        }.resume()
    }



    func signUp(email: String, password: String) -> AnyPublisher<User, Error> {
        let publisher = PassthroughSubject<User, Error>()

        print("🔐 Attempting email/password sign-up...")
        auth.createUser(withEmail: email, password: password) { result, error in
            if let error = error {
                print("❌ Sign-up failed: \(error.localizedDescription)")
                publisher.send(completion: .failure(error))
                return
            }

            if let user = result?.user {
                print("✅ Sign-up successful: \(user.uid)")
                publisher.send(user)
                publisher.send(completion: .finished)
            } else {
                let error = NSError(domain: "FirebaseService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Failed to create account"])
                publisher.send(completion: .failure(error))
            }
        }

        return publisher.eraseToAnyPublisher()
    }

    func signOut() -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        do {
            try auth.signOut()
            print("✅ Sign-out successful")
            publisher.send(())
            publisher.send(completion: .finished)
        } catch {
            print("❌ Sign-out failed: \(error.localizedDescription)")
            publisher.send(completion: .failure(error))
        }

        return publisher.eraseToAnyPublisher()
    }

    func resetPassword(email: String) -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        auth.sendPasswordReset(withEmail: email) { error in
            if let error = error {
                print("❌ Password reset failed: \(error.localizedDescription)")
                publisher.send(completion: .failure(error))
                return
            }

            print("✅ Password reset email sent")
            publisher.send(())
            publisher.send(completion: .finished)
        }

        return publisher.eraseToAnyPublisher()
    }

    // Helper method to check if user is authenticated
    func isUserAuthenticated() -> Bool {
        // Only return true if we have a real Firebase Auth user
        // This ensures Firebase recognizes the user for write operations
        return auth.currentUser != nil
    }

    // Method to get authentication status details for debugging
    func getAuthenticationStatus() -> String {
        let firebaseAuth = auth.currentUser != nil
        let userDefaultsAuth = UserDefaults.standard.bool(forKey: "firebase_auth_success")
        let email = UserDefaults.standard.string(forKey: "firebase_user_email") ?? "None"
        let userId = UserDefaults.standard.string(forKey: "firebase_user_id") ?? "None"
        let firebaseEmail = auth.currentUser?.email ?? "None"
        let firebaseUid = auth.currentUser?.uid ?? "None"

        return """
        🔐 Authentication Status:
        - Firebase Auth: \(firebaseAuth ? "✅ Active" : "❌ Inactive")
        - Firebase Email: \(firebaseEmail)
        - Firebase UID: \(firebaseUid)
        - UserDefaults: \(userDefaultsAuth ? "✅ Stored" : "❌ Not stored")
        - Stored Email: \(email)
        - Stored User ID: \(userId)
        - Overall: \(isUserAuthenticated() ? "✅ Authenticated" : "❌ Not authenticated")
        """
    }

    // MARK: - Drama Operations

    func fetchAllDramas() -> AnyPublisher<[Drama], Error> {
        let publisher = PassthroughSubject<[Drama], Error>()

        // Try to fetch from both collections to cover all bases
        let dramasPublisher = fetchDramasFromCollection("dramas")
        let chatPreviewsPublisher = fetchDramasFromCollection("chatPreviews")

        // Combine results from both collections
        Publishers.CombineLatest(dramasPublisher, chatPreviewsPublisher)
            .map { dramas1, dramas2 -> [Drama] in
                // Combine and remove duplicates based on ID
                let allDramas = dramas1 + dramas2
                var uniqueDramas: [Drama] = []
                var seenIds = Set<String>()

                for drama in allDramas {
                    if let id = drama.id, !seenIds.contains(id) {
                        seenIds.insert(id)
                        uniqueDramas.append(drama)
                    } else if drama.id == nil {
                        uniqueDramas.append(drama)
                    }
                }

                return uniqueDramas.sorted(by: { $0.updatedAt > $1.updatedAt })
            }
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    publisher.send(completion: .failure(error))
                }
            }, receiveValue: { dramas in
                publisher.send(dramas)
            })
            .store(in: &cancellables)

        return publisher.eraseToAnyPublisher()
    }

    private var cancellables = Set<AnyCancellable>()

    private func fetchDramasFromCollection(_ collectionName: String) -> AnyPublisher<[Drama], Error> {
        let publisher = PassthroughSubject<[Drama], Error>()

        db.collection(collectionName).addSnapshotListener { snapshot, error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            guard let documents = snapshot?.documents else {
                publisher.send([])
                return
            }

            print("Found \(documents.count) documents in \(collectionName) collection")

            let dramas = documents.compactMap { document -> Drama? in
                let data = document.data()
                print("Processing document: \(document.documentID) with data: \(data)")

                // Try to adapt to the structure shown in the screenshot
                if let title = data["title"] as? String {
                    let description = data["description"] as? String ?? "No description"

                    // For timestamp, try different formats
                    var timestamp: Date
                    if let timestampField = data["timestamp"] as? Timestamp {
                        timestamp = timestampField.dateValue()
                    } else if let createdAt = data["created_at"] as? Timestamp {
                        timestamp = createdAt.dateValue()
                    } else {
                        timestamp = Date()
                    }

                    // For author, use a default if not available
                    let author = data["author"] as? String ?? "Unknown Author"

                    return Drama(
                        id: document.documentID,
                        title: title,
                        author: author,
                        description: description,
                        coverImageURL: data["cover_image_url"] as? String,
                        chapters: nil,
                        createdAt: timestamp,
                        updatedAt: timestamp
                    )
                }

                // Try the original format as a fallback
                if let title = data["title"] as? String,
                   let author = data["author"] as? String,
                   let description = data["description"] as? String {

                    let createdAt = (data["created_at"] as? Timestamp)?.dateValue() ?? Date()
                    let updatedAt = (data["updated_at"] as? Timestamp)?.dateValue() ?? createdAt

                    var drama = Drama(
                        id: document.documentID,
                        title: title,
                        author: author,
                        description: description,
                        coverImageURL: data["cover_image_url"] as? String,
                        chapters: nil,
                        createdAt: createdAt,
                        updatedAt: updatedAt
                    )

                    // Parse chapters if they exist
                    if let chaptersData = data["chapters"] as? [[String: Any]] {
                        drama.chapters = chaptersData.compactMap { chapterData -> Chapter? in
                            guard let id = chapterData["id"] as? String,
                                  let title = chapterData["title"] as? String,
                                  let order = chapterData["order"] as? Int else {
                                return nil
                            }

                            var chapter = Chapter(id: id, title: title, order: order, scenes: nil)

                            // Parse scenes if they exist
                            if let scenesData = chapterData["scenes"] as? [[String: Any]] {
                                chapter.scenes = scenesData.compactMap { sceneData -> Scene? in
                                    guard let id = sceneData["id"] as? String,
                                          let title = sceneData["title"] as? String,
                                          let order = sceneData["order"] as? Int,
                                          let content = sceneData["content"] as? String else {
                                        return nil
                                    }

                                    return Scene(
                                        id: id,
                                        title: title,
                                        order: order,
                                        content: content,
                                        characters: sceneData["characters"] as? [String]
                                    )
                                }
                            }

                            return chapter
                        }
                    }

                    return drama
                }

                return nil
            }

            print("Successfully parsed \(dramas.count) dramas from \(collectionName)")
            publisher.send(dramas)
        }

        return publisher.eraseToAnyPublisher()
    }

    func fetchDrama(id: String) -> AnyPublisher<Drama, Error> {
        let publisher = PassthroughSubject<Drama, Error>()

        // Try to fetch from both collections
        fetchDramaFromCollection("dramas", id: id)
            .catch { _ in
                // If not found in dramas, try chatPreviews
                return self.fetchDramaFromCollection("chatPreviews", id: id)
            }
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    publisher.send(completion: .failure(error))
                }
            }, receiveValue: { drama in
                publisher.send(drama)
                publisher.send(completion: .finished)
            })
            .store(in: &self.cancellables)

        return publisher.eraseToAnyPublisher()
    }

    private func fetchDramaFromCollection(_ collectionName: String, id: String) -> AnyPublisher<Drama, Error> {
        let publisher = PassthroughSubject<Drama, Error>()

        db.collection(collectionName).document(id).getDocument { document, error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            guard let document = document, document.exists else {
                publisher.send(completion: .failure(NSError(domain: "FirebaseService", code: 404, userInfo: [NSLocalizedDescriptionKey: "Drama not found in \(collectionName)"])))
                return
            }

            let data = document.data() ?? [:]
            print("Found document in \(collectionName): \(document.documentID) with data: \(data)")

            // Try to adapt to the structure shown in the screenshot
            if let title = data["title"] as? String {
                let description = data["description"] as? String ?? "No description"

                // For timestamp, try different formats
                var timestamp: Date
                if let timestampField = data["timestamp"] as? Timestamp {
                    timestamp = timestampField.dateValue()
                } else if let createdAt = data["created_at"] as? Timestamp {
                    timestamp = createdAt.dateValue()
                } else {
                    timestamp = Date()
                }

                // For author, use a default if not available
                let author = data["author"] as? String ?? "Unknown Author"

                let drama = Drama(
                    id: document.documentID,
                    title: title,
                    author: author,
                    description: description,
                    coverImageURL: data["cover_image_url"] as? String,
                    chapters: nil,
                    createdAt: timestamp,
                    updatedAt: timestamp
                )

                publisher.send(drama)
                publisher.send(completion: .finished)
                return
            }

            // Try the original format as a fallback
            if let title = data["title"] as? String,
               let author = data["author"] as? String,
               let description = data["description"] as? String {

                let createdAt = (data["created_at"] as? Timestamp)?.dateValue() ?? Date()
                let updatedAt = (data["updated_at"] as? Timestamp)?.dateValue() ?? createdAt

                var drama = Drama(
                    id: document.documentID,
                    title: title,
                    author: author,
                    description: description,
                    coverImageURL: data["cover_image_url"] as? String,
                    chapters: nil,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )

                // Parse chapters if they exist
                if let chaptersData = data["chapters"] as? [[String: Any]] {
                    drama.chapters = chaptersData.compactMap { chapterData -> Chapter? in
                        guard let id = chapterData["id"] as? String,
                              let title = chapterData["title"] as? String,
                              let order = chapterData["order"] as? Int else {
                            return nil
                        }

                        var chapter = Chapter(id: id, title: title, order: order, scenes: nil)

                        // Parse scenes if they exist
                        if let scenesData = chapterData["scenes"] as? [[String: Any]] {
                            chapter.scenes = scenesData.compactMap { sceneData -> Scene? in
                                guard let id = sceneData["id"] as? String,
                                      let title = sceneData["title"] as? String,
                                      let order = sceneData["order"] as? Int,
                                      let content = sceneData["content"] as? String else {
                                    return nil
                                }

                                return Scene(
                                    id: id,
                                    title: title,
                                    order: order,
                                    content: content,
                                    characters: sceneData["characters"] as? [String]
                                )
                            }
                        }

                        return chapter
                    }
                }

                publisher.send(drama)
                publisher.send(completion: .finished)
                return
            }

            publisher.send(completion: .failure(NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Invalid drama data in \(collectionName)"])))
        }

        return publisher.eraseToAnyPublisher()
    }

    func saveDrama(_ drama: Drama) -> AnyPublisher<Drama, Error> {
        let publisher = PassthroughSubject<Drama, Error>()

        do {
            var updatedDrama = drama
            updatedDrama.updatedAt = Date()

            if drama.id == nil {
                updatedDrama.createdAt = Date()
                let docRef = db.collection("dramas").document()
                let dramaData = dramaToDict(updatedDrama)
                try docRef.setData(dramaData)
                var savedDrama = updatedDrama
                savedDrama.id = docRef.documentID
                publisher.send(savedDrama)
                publisher.send(completion: .finished)
            } else {
                if let id = drama.id {
                    let dramaData = dramaToDict(updatedDrama)
                    try db.collection("dramas").document(id).setData(dramaData)
                    publisher.send(updatedDrama)
                    publisher.send(completion: .finished)
                }
            }
        } catch {
            publisher.send(completion: .failure(error))
        }

        return publisher.eraseToAnyPublisher()
    }

    func deleteDrama(id: String) -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        db.collection("dramas").document(id).delete { error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            publisher.send(())
            publisher.send(completion: .finished)
        }

        return publisher.eraseToAnyPublisher()
    }

    // MARK: - Character Operations

    func fetchAllCharacters() -> AnyPublisher<[Character], Error> {
        let publisher = PassthroughSubject<[Character], Error>()

        // Try to fetch from both collections to cover all bases
        let charactersPublisher = fetchCharactersFromCollection("characters")
        let chatPreviewsPublisher = fetchCharactersFromCollection("chatPreviews")

        // Combine results from both collections
        Publishers.CombineLatest(charactersPublisher, chatPreviewsPublisher)
            .map { characters1, characters2 -> [Character] in
                // Combine and remove duplicates based on ID
                let allCharacters = characters1 + characters2
                var uniqueCharacters: [Character] = []
                var seenIds = Set<String>()

                for character in allCharacters {
                    if let id = character.id, !seenIds.contains(id) {
                        seenIds.insert(id)
                        uniqueCharacters.append(character)
                    } else if character.id == nil {
                        uniqueCharacters.append(character)
                    }
                }

                return uniqueCharacters.sorted(by: { $0.name < $1.name })
            }
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    publisher.send(completion: .failure(error))
                }
            }, receiveValue: { characters in
                publisher.send(characters)
            })
            .store(in: &self.cancellables)

        return publisher.eraseToAnyPublisher()
    }

    private func fetchCharactersFromCollection(_ collectionName: String) -> AnyPublisher<[Character], Error> {
        let publisher = PassthroughSubject<[Character], Error>()

        db.collection(collectionName).addSnapshotListener { snapshot, error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            guard let documents = snapshot?.documents else {
                publisher.send([])
                return
            }

            print("Found \(documents.count) documents in \(collectionName) collection for characters")

            let characters = documents.compactMap { document -> Character? in
                let data = document.data()

                // Try standard format
                if let name = data["name"] as? String {
                    let description = data["description"] as? String ?? "No description"

                    return Character(
                        id: document.documentID,
                        name: name,
                        description: description,
                        imageURL: data["image_url"] as? String
                    )
                }

                // Try alternative format - use title as name
                if let title = data["title"] as? String {
                    let description = data["description"] as? String ?? "No description"

                    return Character(
                        id: document.documentID,
                        name: title,
                        description: description,
                        imageURL: data["image_url"] as? String
                    )
                }

                return nil
            }

            print("Successfully parsed \(characters.count) characters from \(collectionName)")
            publisher.send(characters)
        }

        return publisher.eraseToAnyPublisher()
    }

    func saveCharacter(_ character: Character) -> AnyPublisher<Character, Error> {
        let publisher = PassthroughSubject<Character, Error>()

        do {
            if character.id == nil {
                let docRef = db.collection("characters").document()
                let characterData = characterToDict(character)
                try docRef.setData(characterData)
                var savedCharacter = character
                savedCharacter.id = docRef.documentID
                publisher.send(savedCharacter)
                publisher.send(completion: .finished)
            } else {
                if let id = character.id {
                    let characterData = characterToDict(character)
                    try db.collection("characters").document(id).setData(characterData)
                    publisher.send(character)
                    publisher.send(completion: .finished)
                }
            }
        } catch {
            publisher.send(completion: .failure(error))
        }

        return publisher.eraseToAnyPublisher()
    }

    func deleteCharacter(id: String) -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        db.collection("characters").document(id).delete { error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            publisher.send(())
            publisher.send(completion: .finished)
        }

        return publisher.eraseToAnyPublisher()
    }

    // MARK: - Image Upload Operations

    /// Delete an image from Firebase Storage by URL
    func deleteImageFromStorage(imageURL: String) -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        // Check if the URL is valid
        guard let url = URL(string: imageURL),
              url.host?.contains("firebasestorage.googleapis.com") == true else {
            print("⚠️ Not a Firebase Storage URL or invalid URL format: \(imageURL)")
            publisher.send(())
            publisher.send(completion: .finished)
            return publisher.eraseToAnyPublisher()
        }

        // Extract the path from the URL
        // Firebase Storage URLs typically look like:
        // https://firebasestorage.googleapis.com/v0/b/[PROJECT_ID].appspot.com/o/[PATH]?alt=media&token=[TOKEN]
        guard let pathComponent = url.path.components(separatedBy: "/o/").last,
              let decodedPath = pathComponent.removingPercentEncoding else {
            print("⚠️ Could not extract path from URL: \(imageURL)")
            publisher.send(())
            publisher.send(completion: .finished)
            return publisher.eraseToAnyPublisher()
        }

        // Create a reference to the file
        let storageRef = storage.reference()
        let fileRef = storageRef.child(decodedPath)

        print("🗑️ Attempting to delete image at path: \(fileRef.fullPath)")

        // Delete the file
        fileRef.delete { error in
            if let error = error {
                print("❌ Error deleting image: \(error.localizedDescription)")
                // Don't fail the publisher, just log the error
                // This way, the upload process can continue even if deletion fails
                publisher.send(())
                publisher.send(completion: .finished)
            } else {
                print("✅ Successfully deleted old image")
                publisher.send(())
                publisher.send(completion: .finished)
            }
        }

        return publisher.eraseToAnyPublisher()
    }

    /// Upload an image for a chat and update the chat preview with the image URL
    /// If oldImageURL is provided, it will delete the old image from storage
    func uploadImageForChat(image: NSImage, chatId: String, oldImageURL: String? = nil) -> AnyPublisher<String, Error> {
        let publisher = PassthroughSubject<String, Error>()

        // Check if user is authenticated for write operations
        guard isUserAuthenticated() else {
            let error = NSError(domain: "FirebaseService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Authentication required to upload images. Please sign in from Settings."])
            publisher.send(completion: .failure(error))
            return publisher.eraseToAnyPublisher()
        }

        // Reset upload state
        isUploading = true
        uploadProgress = 0.0

        // If there's an old image URL, delete it first
        let uploadPublisher: AnyPublisher<Void, Error>

        if let oldImageURL = oldImageURL {
            print("🔄 Replacing existing image: \(oldImageURL)")
            uploadPublisher = deleteImageFromStorage(imageURL: oldImageURL)
        } else {
            uploadPublisher = Just(()).setFailureType(to: Error.self).eraseToAnyPublisher()
        }

        // After deletion (or immediately if no old image), proceed with upload
        uploadPublisher
            .flatMap { [weak self] _ -> AnyPublisher<String, Error> in
                guard let self = self else {
                    return Fail(error: NSError(domain: "FirebaseService", code: 500, userInfo: [NSLocalizedDescriptionKey: "Self reference lost"]))
                        .eraseToAnyPublisher()
                }

                // Convert NSImage to Data
                guard let tiffData = image.tiffRepresentation,
                      let bitmapImage = NSBitmapImageRep(data: tiffData),
                      let jpegData = bitmapImage.representation(using: .jpeg, properties: [.compressionFactor: 0.7]) else {
                    self.isUploading = false
                    return Fail(error: NSError(domain: "FirebaseService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Failed to convert image to JPEG data"]))
                        .eraseToAnyPublisher()
                }

                let uploadPublisher = PassthroughSubject<String, Error>()

                // Create a storage reference
                let storageRef = self.storage.reference()

                // Use a more permissive path that doesn't include user-specific information
                // This helps avoid permission issues until proper rules are set up
                let imagesRef = storageRef.child("public_images")
                let fileRef = imagesRef.child("chat_\(chatId)_\(UUID().uuidString).jpg")

                // Set metadata to public read
                let metadata = StorageMetadata()
                metadata.contentType = "image/jpeg"

                print("⬆️ Uploading image to path: \(fileRef.fullPath)")

                // Create the upload task
                let uploadTask = fileRef.putData(jpegData, metadata: metadata) { [weak self] metadata, error in
                    guard let self = self else { return }

                    self.isUploading = false

                    if let error = error {
                        print("❌ Upload error: \(error.localizedDescription)")

                        // Check for permission error
                        if error.localizedDescription.contains("permission") || error.localizedDescription.contains("access") {
                            let permissionError = NSError(
                                domain: "FirebaseService",
                                code: 403,
                                userInfo: [
                                    NSLocalizedDescriptionKey: "Permission denied: Please check Firebase Storage rules. Make sure they allow write access to the 'public_images' folder."
                                ]
                            )
                            uploadPublisher.send(completion: .failure(permissionError))
                        } else {
                            uploadPublisher.send(completion: .failure(error))
                        }
                        return
                    }

                    print("✅ Image uploaded successfully, getting download URL")

                    // Get the download URL
                    fileRef.downloadURL { url, error in
                        if let error = error {
                            print("❌ Failed to get download URL: \(error.localizedDescription)")
                            uploadPublisher.send(completion: .failure(error))
                            return
                        }

                        guard let downloadURL = url else {
                            let error = NSError(domain: "FirebaseService", code: 404, userInfo: [NSLocalizedDescriptionKey: "Failed to get download URL"])
                            uploadPublisher.send(completion: .failure(error))
                            return
                        }

                        print("🔗 Got download URL: \(downloadURL.absoluteString)")

                        // Update the chat preview with the image URL
                        self.updateChatWithImageURL(chatId: chatId, imageURL: downloadURL.absoluteString)
                            .sink(
                                receiveCompletion: { completion in
                                    if case .failure(let error) = completion {
                                        print("❌ Failed to update chat with image URL: \(error.localizedDescription)")
                                        uploadPublisher.send(completion: .failure(error))
                                    } else {
                                        print("✅ Successfully updated chat with image URL")
                                        uploadPublisher.send(completion: .finished)
                                    }
                                },
                                receiveValue: { _ in
                                    uploadPublisher.send(downloadURL.absoluteString)
                                }
                            )
                            .store(in: &self.cancellables)
                    }
                }

                // Monitor upload progress
                uploadTask.observe(.progress) { [weak self] snapshot in
                    guard let self = self,
                          let progress = snapshot.progress else { return }

                    DispatchQueue.main.async {
                        self.uploadProgress = Double(progress.completedUnitCount) / Double(progress.totalUnitCount)
                        print("📊 Upload progress: \(Int(self.uploadProgress * 100))%")
                    }
                }

                return uploadPublisher.eraseToAnyPublisher()
            }
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        publisher.send(completion: .failure(error))
                    }
                },
                receiveValue: { imageURL in
                    publisher.send(imageURL)
                    publisher.send(completion: .finished)
                }
            )
            .store(in: &cancellables)

        return publisher.eraseToAnyPublisher()
    }

    /// Update a chat preview with an image URL
    private func updateChatWithImageURL(chatId: String, imageURL: String) -> AnyPublisher<Void, Error> {
        let publisher = PassthroughSubject<Void, Error>()

        let chatRef = db.collection("chatPreviews").document(chatId)

        chatRef.updateData([
            "image_url": imageURL
        ]) { error in
            if let error = error {
                publisher.send(completion: .failure(error))
                return
            }

            publisher.send(())
            publisher.send(completion: .finished)
        }

        return publisher.eraseToAnyPublisher()
    }

    /// Print simple Firebase Storage rules (no authentication required)
    func printFirebaseStorageRules() {
        print("""

        📂 Simple Firebase Storage Rules (No Authentication) 📂

        Use these PUBLIC rules in your Firebase Storage:

        rules_version = '2';
        service firebase.storage {
          match /b/{bucket}/o {
            // Allow public read access to all files
            match /{allPaths=**} {
              allow read: if true;
            }

            // Allow public write access to image folders
            match /public_images/{imageId} {
              allow write: if true;
            }

            match /chat_images/{imageId} {
              allow write: if true;
            }
          }
        }

        Apply these rules in Firebase Console:
        1. Go to https://console.firebase.google.com/
        2. Select your project
        3. Go to Storage > Rules
        4. Paste the rules above
        5. Click "Publish"

        Features:
        ✅ No authentication required
        ✅ Public read/write access
        ✅ Simple and works immediately
        ⚠️ Note: These are development rules - not for production

        """)
    }
}
