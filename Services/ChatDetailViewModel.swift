import Foundation
import FirebaseFirestore
import SwiftUI
import Combine

class ChatDetailViewModel: ObservableObject {
    @Published var messages: [Message] = []
    @Published var chatImageURL: String?
    @Published var isUploading = false
    @Published var uploadProgress: Double = 0.0
    @Published var showImagePicker = false
    @Published var selectedImage: NSImage?
    @Published var errorMessage: String?
    @Published var showError = false

    private let db = Firestore.firestore()
    private var chatId: String
    private var cancellables = Set<AnyCancellable>()
    private let firebaseService: FirebaseService

    init(chatId: String, firebaseService: FirebaseService) {
        self.chatId = chatId
        self.firebaseService = firebaseService
        fetchMessages()
        fetchChatDetails()
    }

    func fetchMessages() {
        db.collection("dramas")
            .document(chatId)
            .collection("messages")
            .order(by: "timestamp", descending: false)
            .addSnapshotListener { [weak self] querySnapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("Error fetching messages: \(error.localizedDescription)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("No messages found")
                    return
                }

                self.messages = documents.compactMap { document in
                    do {
                        var message = try document.data(as: Message.self)
                        message.id = document.documentID
                        return message
                    } catch {
                        print("Error decoding message: \(error)")
                        return nil
                    }
                }
            }
    }

    func fetchChatDetails() {
        db.collection("chatPreviews")
            .document(chatId)
            .addSnapshotListener { [weak self] documentSnapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("Error fetching chat details: \(error.localizedDescription)")
                    return
                }

                guard let document = documentSnapshot, document.exists else {
                    print("Chat document not found")
                    return
                }

                if let imageURL = document.data()?["image_url"] as? String {
                    self.chatImageURL = imageURL
                }
            }
    }

    func uploadImage() {
        guard let selectedImage = selectedImage else {
            errorMessage = "No image selected"
            showError = true
            return
        }

        isUploading = true

        // Bind to the Firebase service's upload progress
        firebaseService.$uploadProgress
            .sink { [weak self] progress in
                self?.uploadProgress = progress
            }
            .store(in: &cancellables)

        // Print Firebase Storage rules to help with setup
        firebaseService.printFirebaseStorageRules()

        // Pass the current image URL if it exists so the old image can be deleted
        firebaseService.uploadImageForChat(image: selectedImage, chatId: chatId, oldImageURL: chatImageURL)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }

                    self.isUploading = false

                    if case .failure(let error) = completion {
                        // Check if it's a permission error
                        if error.localizedDescription.contains("Permission denied") ||
                           error.localizedDescription.contains("permission") ||
                           error.localizedDescription.contains("access") {
                            self.errorMessage = """
                            Firebase Storage permission error: \(error.localizedDescription)

                            Please update your Firebase Storage rules in the Firebase Console to allow image uploads.
                            """
                        } else {
                            self.errorMessage = "Failed to upload image: \(error.localizedDescription)"
                        }
                        self.showError = true
                    }
                },
                receiveValue: { [weak self] imageURL in
                    guard let self = self else { return }

                    self.chatImageURL = imageURL
                    self.selectedImage = nil
                }
            )
            .store(in: &cancellables)
    }
}
