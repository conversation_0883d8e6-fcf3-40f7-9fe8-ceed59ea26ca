<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firestore Rules for Text Drama Publisher</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #4285f4;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
        }
        h2 {
            color: #ea4335;
            margin-top: 30px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #ffeeba;
            margin: 20px 0;
        }
        .steps {
            background-color: #e9f5ff;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #4285f4;
            margin: 20px 0;
        }
        ol {
            padding-left: 20px;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3367d6;
        }
    </style>
</head>
<body>
    <h1>Firestore Rules for Text Drama Publisher</h1>
    
    <p>Your app is connected to Firebase but isn't fetching any data. This is likely because the Firestore security rules are preventing access to your data.</p>
    
    <div class="warning">
        <strong>Warning:</strong> The rules below allow unrestricted read and write access to your Firestore database. This is fine for development but should be replaced with proper authentication rules before deploying to production.
    </div>
    
    <h2>Rules to Copy</h2>
    
    <pre><code>rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      // Allow read/write access to all users
      allow read, write: if true;
    }
  }
}</code></pre>

    <button onclick="copyRules()">Copy Rules</button>
    
    <h2>How to Update Your Firestore Rules</h2>
    
    <div class="steps">
        <ol>
            <li>Go to the <a href="https://console.firebase.google.com/" target="_blank">Firebase Console</a></li>
            <li>Select your project (text-drama)</li>
            <li>In the left sidebar, click on "Firestore Database"</li>
            <li>Click on the "Rules" tab</li>
            <li>Delete the existing rules</li>
            <li>Paste the rules from above</li>
            <li>Click "Publish"</li>
        </ol>
    </div>
    
    <h2>Next Steps</h2>
    
    <p>After updating your rules, restart your app and check if data is being fetched correctly. If you're still having issues, you might need to:</p>
    
    <ul>
        <li>Check if you have data in your Firestore database</li>
        <li>Verify that your app is using the correct collection names ("dramas" and "characters")</li>
        <li>Make sure your app is properly authenticated with Firebase</li>
    </ul>
    
    <h2>Secure Your Database Later</h2>
    
    <p>Once your app is working correctly, you should update your security rules to only allow authenticated users to access your data. Here's an example of more secure rules:</p>
    
    <pre><code>rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}</code></pre>

    <script>
        function copyRules() {
            const rulesText = `rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      // Allow read/write access to all users
      allow read, write: if true;
    }
  }
}`;
            navigator.clipboard.writeText(rulesText).then(() => {
                alert("Rules copied to clipboard!");
            }, (err) => {
                console.error('Could not copy text: ', err);
                alert("Failed to copy rules. Please select and copy manually.");
            });
        }
    </script>
</body>
</html>
