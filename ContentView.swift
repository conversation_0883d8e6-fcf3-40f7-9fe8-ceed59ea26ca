//
//  ContentView.swift
//  Text_Drama_Publisher
//
//  Created by <PERSON><PERSON><PERSON> on 5/16/25.
//

import SwiftUI
import Combine
import FirebaseCore
import FirebaseFirestore

struct ContentView: View {
    @ObservedObject private var firebaseService = FirebaseService.shared
    @State private var selectedNavItem: NavItem? = .chats
    @State private var errorMessage: String? = nil
    @State private var isLoading = false
    @State private var showError = false

    @State private var cancellables = Set<AnyCancellable>()

    enum NavItem: String, Identifiable {
        case chats = "Chats"
        case aiGeneration = "AI Generation"
        case settings = "Settings"

        var id: String { self.rawValue }

        var icon: String {
            switch self {
            case .chats: return "message.fill"
            case .aiGeneration: return "wand.and.stars"
            case .settings: return "gear"
            }
        }
    }

    var body: some View {
        NavigationSplitView {
            List(selection: $selectedNavItem) {
                Section("Content") {
                    NavigationLink(value: NavItem.chats) {
                        Label("Chats", systemImage: "message.fill")
                    }

                    NavigationLink(value: NavItem.aiGeneration) {
                        Label("AI Generation", systemImage: "wand.and.stars")
                    }
                }

                Section("App") {
                    NavigationLink(value: NavItem.settings) {
                        Label("Settings", systemImage: "gear")
                    }
                }
            }
            .navigationTitle("Text Drama Publisher")
        } detail: {
            if let selectedItem = selectedNavItem {
                switch selectedItem {
                case .chats:
                    ChatListView()
                case .aiGeneration:
                    AIGenerationView()
                        .padding()
                case .settings:
                    SettingsView()
                }
            } else {
                Text("Select an item from the sidebar")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            // Firebase connection will be established automatically
            // through the FirebaseService initialization
        }
        .alert("Error", isPresented: $showError, actions: {
            Button("OK") {
                errorMessage = nil
                showError = false
            }
        }, message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            } else {
                Text("An unknown error occurred")
            }
        })
    }
}

// MARK: - Preview

#Preview {
    ContentView()
}
