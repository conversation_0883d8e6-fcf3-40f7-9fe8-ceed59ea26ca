# Firebase Firestore Rules Update Instructions

## The Problem
Your app is getting "Missing or insufficient permissions" errors when trying to write to Firebase Firestore.

## The Solution
You need to update your Firestore security rules to allow read/write access.

## Step-by-Step Instructions:

### 1. Open Firebase Console
- Go to: https://console.firebase.google.com/
- Select your project: **text-drama**

### 2. Navigate to Firestore Rules
- In the left sidebar, click **"Firestore Database"**
- Click on the **"Rules"** tab at the top

### 3. Replace the Current Rules
Delete all existing rules and replace with:

```
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

### 4. Publish the Rules
- Click the **"Publish"** button
- Wait for the confirmation message

### 5. Verify the Rules
Your rules should now look exactly like the code above. This allows unrestricted read/write access to your Firestore database.

## Important Notes:
- These rules allow anyone to read/write your database
- This is fine for development but should be secured before production
- After your app is working, you can update to more secure rules

## Alternative: Storage Rules (if using images)
If you're also having issues with image uploads, update Storage rules:

1. Go to **Storage** > **Rules** in Firebase Console
2. Replace with:
```
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true;
    }
  }
}
```

## Test After Update
After updating the rules, restart your app and try the chat continuation feature again.
