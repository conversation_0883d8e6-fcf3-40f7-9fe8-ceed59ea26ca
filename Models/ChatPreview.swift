import Foundation
import FirebaseFirestore

struct ChatPreview: Identifiable, Codable, Hashable {
    var id: String?
    let title: String
    let description: String
    let timestamp: Timestamp
    let messageCount: Int
    let categories: [String]?
    let imageURL: String?

    enum CodingKeys: String, CodingKey {
        case id
        case title
        case description
        case timestamp
        case messageCount
        case categories
        case imageURL = "image_url"
    }

    // Custom Hashable implementation to handle optional id
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(title)
        hasher.combine(description)
        hasher.combine(timestamp.seconds)
        hasher.combine(messageCount)
        hasher.combine(categories)
        hasher.combine(imageURL)
    }

    // Custom Equatable implementation
    static func == (lhs: ChatPreview, rhs: ChatPreview) -> Bool {
        // If both have IDs, compare by ID
        if let lhsId = lhs.id, let rhsId = rhs.id {
            return lhsId == rhsId
        }

        // Otherwise compare all properties
        return lhs.title == rhs.title &&
               lhs.description == rhs.description &&
               lhs.timestamp.seconds == rhs.timestamp.seconds &&
               lhs.messageCount == rhs.messageCount &&
               lhs.categories == rhs.categories &&
               lhs.imageURL == rhs.imageURL
    }
}
