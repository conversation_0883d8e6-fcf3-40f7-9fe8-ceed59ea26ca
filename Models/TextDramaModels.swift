//
//  TextDramaModels.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import Foundation
import FirebaseFirestore

// Main Drama document model
struct Drama: Identifiable, Codable, Hashable {
    var id: String?
    var title: String
    var author: String
    var description: String
    var coverImageURL: String?
    var chapters: [Chapter]?
    var createdAt: Date
    var updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case author
        case description
        case coverImageURL = "cover_image_url"
        case chapters
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// Chapter model
struct Chapter: Identifiable, Codable, Hashable {
    var id: String
    var title: String
    var order: Int
    var scenes: [Scene]?
    
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case order
        case scenes
    }
}

// Scene model
struct Scene: Identifiable, Codable, Hashable {
    var id: String
    var title: String
    var order: Int
    var content: String
    var characters: [String]?
    
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case order
        case content
        case characters
    }
}

// Character model
struct Character: Identifiable, Codable, Hashable {
    var id: String?
    var name: String
    var description: String
    var imageURL: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case description
        case imageURL = "image_url"
    }
}
