<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>API_KEY</key>
	<string>AIzaSyBx1vYVyV8uEOzYkwRbMVifWyBdSfhZeUM</string>
	<key>BUNDLE_ID</key>
	<string>risul.rashed.com.Text-Drama-Publisher</string>
	<key>DATABASE_URL</key>
	<string>https://text-drama-default-rtdb.firebaseio.com</string>
	<key>GCM_SENDER_ID</key>
	<string>847567038073</string>
	<key>GOOGLE_APP_ID</key>
	<string>1:847567038073:ios:bb7e9c895603361063d6a8</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<false/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>PROJECT_ID</key>
	<string>text-drama</string>
	<key>STORAGE_BUCKET</key>
	<string>text-drama.firebasestorage.app</string>
</dict>
</plist>
