//
//  SampleDataAdder.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import Combine
import FirebaseFirestore

struct SampleDataAdder: View {
    var onComplete: () -> Void

    @State private var isAdding: Bool = false
    @State private var errorMessage: String? = nil
    @State private var successMessage: String? = nil
    @State private var cancellables = Set<AnyCancellable>()

    init(onComplete: @escaping () -> Void) {
        self.onComplete = onComplete
    }

    var body: some View {
        VStack(spacing: 20) {
            Text("Add Sample Data")
                .font(.title)
                .fontWeight(.bold)

            if isAdding {
                ProgressView()
                    .scaleEffect(1.5)
                Text("Adding sample data to Firestore...")
                    .font(.headline)
            } else if let success = successMessage {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
                Text(success)
                    .font(.headline)
                    .multilineTextAlignment(.center)
                    .padding()
            } else {
                Text("This will add sample dramas and characters to your Firestore database.")
                    .multilineTextAlignment(.center)
                    .padding()

                Button(action: {
                    addSampleData()
                }) {
                    Text("Add Sample Data")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding(.horizontal)
            }

            if let error = errorMessage {
                Text("Error: \(error)")
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
            }

            Spacer()

            Button(action: {
                onComplete()
            }) {
                Text("Close")
                    .fontWeight(.semibold)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.primary)
                    .cornerRadius(10)
            }
            .padding(.horizontal)
        }
        .padding()
        .frame(width: 500, height: 400)
    }

    private func addSampleData() {
        isAdding = true
        errorMessage = nil
        print("Adding sample data to Firestore...")

        let db = Firestore.firestore()

        // Add sample dramas
        let drama1: [String: Any] = [
            "title": "Romeo and Juliet",
            "author": "William Shakespeare",
            "description": "A tragedy about two young lovers whose deaths ultimately reconcile their feuding families.",
            "created_at": Timestamp(date: Date()),
            "updated_at": Timestamp(date: Date()),
            "chapters": [
                [
                    "id": UUID().uuidString,
                    "title": "Act 1",
                    "order": 1,
                    "scenes": [
                        [
                            "id": UUID().uuidString,
                            "title": "Scene 1",
                            "order": 1,
                            "content": "Two households, both alike in dignity,\nIn fair Verona, where we lay our scene,\nFrom ancient grudge break to new mutiny,\nWhere civil blood makes civil hands unclean."
                        ]
                    ]
                ]
            ]
        ]

        let drama2: [String: Any] = [
            "title": "Hamlet",
            "author": "William Shakespeare",
            "description": "The tragedy of Hamlet, Prince of Denmark.",
            "created_at": Timestamp(date: Date()),
            "updated_at": Timestamp(date: Date()),
            "chapters": [
                [
                    "id": UUID().uuidString,
                    "title": "Act 1",
                    "order": 1,
                    "scenes": [
                        [
                            "id": UUID().uuidString,
                            "title": "Scene 1",
                            "order": 1,
                            "content": "Who's there?"
                        ]
                    ]
                ]
            ]
        ]

        // Add sample characters
        let character1: [String: String] = [
            "name": "Romeo",
            "description": "Son of Montague and lover of Juliet."
        ]

        let character2: [String: String] = [
            "name": "Juliet",
            "description": "Daughter of Capulet and lover of Romeo."
        ]

        let character3: [String: String] = [
            "name": "Hamlet",
            "description": "Prince of Denmark, son of the late King Hamlet."
        ]

        // Add dramas to Firestore
        db.collection("dramas").document().setData(drama1) { error in
            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Error adding drama 1: \(error.localizedDescription)"
                    self.isAdding = false
                }
                return
            }

            db.collection("dramas").document().setData(drama2) { error in
                if let error = error {
                    DispatchQueue.main.async {
                        self.errorMessage = "Error adding drama 2: \(error.localizedDescription)"
                        self.isAdding = false
                    }
                    return
                }

                // Add characters to Firestore
                db.collection("characters").document().setData(character1) { error in
                    if let error = error {
                        DispatchQueue.main.async {
                            self.errorMessage = "Error adding character 1: \(error.localizedDescription)"
                            self.isAdding = false
                        }
                        return
                    }

                    db.collection("characters").document().setData(character2) { error in
                        if let error = error {
                            DispatchQueue.main.async {
                                self.errorMessage = "Error adding character 2: \(error.localizedDescription)"
                                self.isAdding = false
                            }
                            return
                        }

                        db.collection("characters").document().setData(character3) { error in
                            DispatchQueue.main.async {
                                if let error = error {
                                    self.errorMessage = "Error adding character 3: \(error.localizedDescription)"
                                } else {
                                    self.successMessage = "Sample data added successfully!"
                                    print("Sample data added successfully!")
                                }
                                self.isAdding = false
                            }
                        }
                    }
                }
            }
        }
    }
}
