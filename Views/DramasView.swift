//
//  DramasView.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import Combine

struct DramasView: View {
    @ObservedObject var firebaseService: FirebaseService
    @State private var dramas: [Drama] = []
    @State private var selectedDrama: Drama?
    @State private var isAddingNewDrama = false
    @State private var isEditing = false
    @State private var errorMessage: String?
    @State private var isLoading = false
    @State private var showError = false

    // Use @State to make cancellables mutable
    @State private var cancellables = Set<AnyCancellable>()

    init(firebaseService: FirebaseService) {
        self.firebaseService = firebaseService
    }

    var body: some View {
        NavigationSplitView {
            ZStack {
                List(selection: $selectedDrama) {
                    if isLoading && dramas.isEmpty {
                        HStack {
                            Spacer()
                            Text("Loading dramas...")
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    } else if dramas.isEmpty {
                        HStack {
                            Spacer()
                            Text("No dramas found")
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    } else {
                        ForEach(dramas) { drama in
                            NavigationLink(value: drama) {
                                VStack(alignment: .leading) {
                                    Text(drama.title)
                                        .font(.headline)
                                    Text("By \(drama.author)")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }

                if isLoading && !dramas.isEmpty {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            ProgressView()
                                .scaleEffect(1.5)
                            Spacer()
                        }
                        Spacer()
                    }
                    .background(Color.black.opacity(0.05))
                }
            }
            .navigationTitle("Dramas")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: {
                        isAddingNewDrama = true
                    }) {
                        Label("Add Drama", systemImage: "plus")
                    }
                }

                if selectedDrama != nil {
                    ToolbarItem(placement: .primaryAction) {
                        Button(action: {
                            isEditing = true
                        }) {
                            Label("Edit", systemImage: "pencil")
                        }
                    }

                    ToolbarItem(placement: .primaryAction) {
                        Button(action: {
                            if let drama = selectedDrama, let id = drama.id {
                                deleteDrama(id: id)
                            }
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                        .foregroundColor(.red)
                    }
                }
            }
        } detail: {
            if let drama = selectedDrama {
                DramaDetailView(drama: drama)
            } else {
                Text("Select a drama from the list or create a new one")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            loadDramas()
        }
        .sheet(isPresented: $isAddingNewDrama) {
            DramaEditorView(firebaseService: firebaseService, drama: nil) { newDrama in
                loadDramas()
                selectedDrama = newDrama
            }
        }
        .sheet(isPresented: $isEditing) {
            if let drama = selectedDrama {
                DramaEditorView(firebaseService: firebaseService, drama: drama) { updatedDrama in
                    loadDramas()
                    selectedDrama = updatedDrama
                }
            }
        }
        .alert("Error", isPresented: $showError, actions: {
            Button("OK") {
                errorMessage = nil
                showError = false
            }
        }, message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            } else {
                Text("An unknown error occurred")
            }
        })
    }

    private func loadDramas() {
        isLoading = true
        print("Loading dramas...")

        firebaseService.fetchAllDramas()
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                isLoading = false
                if case .failure(let error) = completion {
                    print("Error loading dramas: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                } else {
                    print("Successfully completed loading dramas")
                }
            }, receiveValue: { fetchedDramas in
                print("Received \(fetchedDramas.count) dramas")
                dramas = fetchedDramas
            })
            .store(in: &cancellables)
    }

    private func deleteDrama(id: String) {
        isLoading = true
        print("Deleting drama with ID: \(id)")

        firebaseService.deleteDrama(id: id)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("Error deleting drama: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                    isLoading = false
                } else {
                    print("Successfully deleted drama")
                }
            }, receiveValue: { _ in
                selectedDrama = nil
                loadDramas()
            })
            .store(in: &cancellables)
    }
}

struct DramaDetailView: View {
    let drama: Drama

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text(drama.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("By \(drama.author)")
                    .font(.title2)
                    .foregroundColor(.secondary)

                Divider()

                Text("Description")
                    .font(.headline)

                Text(drama.description)
                    .font(.body)

                if let chapters = drama.chapters, !chapters.isEmpty {
                    Divider()

                    Text("Chapters")
                        .font(.headline)

                    ForEach(chapters.sorted(by: { $0.order < $1.order })) { chapter in
                        VStack(alignment: .leading) {
                            Text("\(chapter.order). \(chapter.title)")
                                .font(.title3)
                                .fontWeight(.semibold)

                            if let scenes = chapter.scenes, !scenes.isEmpty {
                                ForEach(scenes.sorted(by: { $0.order < $1.order })) { scene in
                                    HStack {
                                        Text("Scene \(scene.order):")
                                            .fontWeight(.medium)
                                        Text(scene.title)
                                    }
                                    .padding(.leading)
                                }
                            }
                        }
                        .padding(.vertical, 5)
                    }
                }
            }
            .padding()
        }
    }
}

struct DramaEditorView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var firebaseService: FirebaseService

    // Drama properties
    @State private var title: String
    @State private var author: String
    @State private var description: String
    @State private var chapters: [Chapter]

    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showError = false

    @State private var cancellables = Set<AnyCancellable>()
    private let onSave: (Drama) -> Void
    private let existingDrama: Drama?

    init(firebaseService: FirebaseService, drama: Drama?, onSave: @escaping (Drama) -> Void) {
        self.firebaseService = firebaseService
        self.onSave = onSave
        self.existingDrama = drama

        _title = State(initialValue: drama?.title ?? "")
        _author = State(initialValue: drama?.author ?? "")
        _description = State(initialValue: drama?.description ?? "")
        _chapters = State(initialValue: drama?.chapters ?? [])
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Drama Details")) {
                    TextField("Title", text: $title)
                    TextField("Author", text: $author)

                    ZStack(alignment: .topLeading) {
                        if description.isEmpty {
                            Text("Description")
                                .foregroundColor(.gray.opacity(0.5))
                                .padding(.top, 8)
                                .padding(.leading, 4)
                        }

                        TextEditor(text: $description)
                            .frame(minHeight: 100)
                    }
                }

                Section(header: Text("Chapters")) {
                    ForEach(chapters.indices, id: \.self) { index in
                        VStack(alignment: .leading) {
                            TextField("Chapter Title", text: Binding(
                                get: { chapters[index].title },
                                set: { chapters[index].title = $0 }
                            ))

                            Stepper("Order: \(chapters[index].order)", value: Binding(
                                get: { chapters[index].order },
                                set: { chapters[index].order = $0 }
                            ))
                        }
                    }
                    .onDelete { indexSet in
                        chapters.remove(atOffsets: indexSet)
                    }

                    Button(action: {
                        let newChapter = Chapter(
                            id: UUID().uuidString,
                            title: "New Chapter",
                            order: chapters.count + 1,
                            scenes: []
                        )
                        chapters.append(newChapter)
                    }) {
                        Label("Add Chapter", systemImage: "plus")
                    }
                }
            }
            .navigationTitle(existingDrama == nil ? "New Drama" : "Edit Drama")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        saveDrama()
                    }
                    .disabled(title.isEmpty || author.isEmpty)
                }
            }
            .alert("Error", isPresented: .init(
                get: { self.errorMessage != nil },
                set: { if !$0 { self.errorMessage = nil } }
            ), actions: {
                Button("OK") {
                    self.errorMessage = nil
                }
            }, message: {
                if let errorMessage = self.errorMessage {
                    Text(errorMessage)
                } else {
                    Text("An unknown error occurred")
                }
            })
        }
        .frame(minWidth: 600, minHeight: 500)
    }

    private func saveDrama() {
        isLoading = true
        print("Saving drama: \(title)")

        let drama = Drama(
            id: existingDrama?.id,
            title: title,
            author: author,
            description: description,
            coverImageURL: existingDrama?.coverImageURL,
            chapters: chapters,
            createdAt: existingDrama?.createdAt ?? Date(),
            updatedAt: Date()
        )

        firebaseService.saveDrama(drama)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                isLoading = false
                if case .failure(let error) = completion {
                    print("Error saving drama: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                } else {
                    print("Successfully saved drama")
                }
            }, receiveValue: { savedDrama in
                print("Received saved drama with ID: \(savedDrama.id ?? "unknown")")
                onSave(savedDrama)
                dismiss()
            })
            .store(in: &cancellables)
    }
}
