//
//  CharactersView.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import Combine

struct CharactersView: View {
    @ObservedObject var firebaseService: FirebaseService
    @State private var characters: [Character] = []
    @State private var selectedCharacter: Character?
    @State private var isAddingNewCharacter = false
    @State private var isEditing = false
    @State private var errorMessage: String?
    @State private var isLoading = false
    @State private var showError = false

    // Use @State to make cancellables mutable
    @State private var cancellables = Set<AnyCancellable>()

    init(firebaseService: FirebaseService) {
        self.firebaseService = firebaseService
    }

    var body: some View {
        NavigationSplitView {
            ZStack {
                List(selection: $selectedCharacter) {
                    if isLoading && characters.isEmpty {
                        HStack {
                            Spacer()
                            Text("Loading characters...")
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    } else if characters.isEmpty {
                        HStack {
                            Spacer()
                            Text("No characters found")
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    } else {
                        ForEach(characters) { character in
                            NavigationLink(value: character) {
                                Text(character.name)
                            }
                        }
                    }
                }

                if isLoading && !characters.isEmpty {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            ProgressView()
                                .scaleEffect(1.5)
                            Spacer()
                        }
                        Spacer()
                    }
                    .background(Color.black.opacity(0.05))
                }
            }
            .navigationTitle("Characters")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: {
                        isAddingNewCharacter = true
                    }) {
                        Label("Add Character", systemImage: "plus")
                    }
                }

                if selectedCharacter != nil {
                    ToolbarItem(placement: .primaryAction) {
                        Button(action: {
                            isEditing = true
                        }) {
                            Label("Edit", systemImage: "pencil")
                        }
                    }

                    ToolbarItem(placement: .primaryAction) {
                        Button(action: {
                            if let character = selectedCharacter, let id = character.id {
                                deleteCharacter(id: id)
                            }
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                        .foregroundColor(.red)
                    }
                }
            }
        } detail: {
            if let character = selectedCharacter {
                CharacterDetailView(character: character)
            } else {
                Text("Select a character from the list or create a new one")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
        }
        .onAppear {
            loadCharacters()
        }
        .sheet(isPresented: $isAddingNewCharacter) {
            CharacterEditorView(firebaseService: firebaseService, character: nil) { newCharacter in
                loadCharacters()
                selectedCharacter = newCharacter
            }
        }
        .sheet(isPresented: $isEditing) {
            if let character = selectedCharacter {
                CharacterEditorView(firebaseService: firebaseService, character: character) { updatedCharacter in
                    loadCharacters()
                    selectedCharacter = updatedCharacter
                }
            }
        }
        .alert("Error", isPresented: $showError, actions: {
            Button("OK") {
                errorMessage = nil
                showError = false
            }
        }, message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            } else {
                Text("An unknown error occurred")
            }
        })
    }

    private func loadCharacters() {
        isLoading = true
        print("Loading characters...")

        firebaseService.fetchAllCharacters()
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                isLoading = false
                if case .failure(let error) = completion {
                    print("Error loading characters: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                } else {
                    print("Successfully completed loading characters")
                }
            }, receiveValue: { fetchedCharacters in
                print("Received \(fetchedCharacters.count) characters")
                characters = fetchedCharacters
            })
            .store(in: &cancellables)
    }

    private func deleteCharacter(id: String) {
        isLoading = true
        print("Deleting character with ID: \(id)")

        firebaseService.deleteCharacter(id: id)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    print("Error deleting character: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                    isLoading = false
                } else {
                    print("Successfully deleted character")
                }
            }, receiveValue: { _ in
                selectedCharacter = nil
                loadCharacters()
            })
            .store(in: &cancellables)
    }
}

struct CharacterDetailView: View {
    let character: Character

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                HStack(alignment: .top) {
                    if let imageURL = character.imageURL, let url = URL(string: imageURL) {
                        AsyncImage(url: url) { phase in
                            switch phase {
                            case .empty:
                                ProgressView()
                                    .frame(width: 150, height: 150)
                            case .success(let image):
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 150, height: 150)
                                    .clipShape(RoundedRectangle(cornerRadius: 10))
                            case .failure:
                                Image(systemName: "person.fill")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 150, height: 150)
                                    .foregroundColor(.gray)
                            @unknown default:
                                EmptyView()
                            }
                        }
                    } else {
                        Image(systemName: "person.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 150, height: 150)
                            .foregroundColor(.gray)
                    }

                    VStack(alignment: .leading) {
                        Text(character.name)
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        Divider()

                        Text("Description")
                            .font(.headline)

                        Text(character.description)
                            .font(.body)
                    }
                    .padding(.leading)
                }
            }
            .padding()
        }
    }
}

struct CharacterEditorView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var firebaseService: FirebaseService

    // Character properties
    @State private var name: String
    @State private var description: String
    @State private var imageURL: String

    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showError = false

    @State private var cancellables = Set<AnyCancellable>()
    private let onSave: (Character) -> Void
    private let existingCharacter: Character?

    init(firebaseService: FirebaseService, character: Character?, onSave: @escaping (Character) -> Void) {
        self.firebaseService = firebaseService
        self.onSave = onSave
        self.existingCharacter = character

        _name = State(initialValue: character?.name ?? "")
        _description = State(initialValue: character?.description ?? "")
        _imageURL = State(initialValue: character?.imageURL ?? "")
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Character Details")) {
                    TextField("Name", text: $name)

                    ZStack(alignment: .topLeading) {
                        if description.isEmpty {
                            Text("Description")
                                .foregroundColor(.gray.opacity(0.5))
                                .padding(.top, 8)
                                .padding(.leading, 4)
                        }

                        TextEditor(text: $description)
                            .frame(minHeight: 100)
                    }

                    TextField("Image URL", text: $imageURL)

                    if !imageURL.isEmpty, let url = URL(string: imageURL) {
                        AsyncImage(url: url) { phase in
                            switch phase {
                            case .empty:
                                ProgressView()
                                    .frame(height: 200)
                            case .success(let image):
                                image
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(height: 200)
                            case .failure:
                                Text("Invalid image URL")
                                    .foregroundColor(.red)
                            @unknown default:
                                EmptyView()
                            }
                        }
                    }
                }
            }
            .navigationTitle(existingCharacter == nil ? "New Character" : "Edit Character")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        saveCharacter()
                    }
                    .disabled(name.isEmpty)
                }
            }
            .alert("Error", isPresented: .init(
                get: { self.errorMessage != nil },
                set: { if !$0 { self.errorMessage = nil } }
            ), actions: {
                Button("OK") {
                    self.errorMessage = nil
                }
            }, message: {
                if let errorMessage = self.errorMessage {
                    Text(errorMessage)
                } else {
                    Text("An unknown error occurred")
                }
            })
        }
        .frame(minWidth: 500, minHeight: 400)
    }

    private func saveCharacter() {
        isLoading = true
        print("Saving character: \(name)")

        let character = Character(
            id: existingCharacter?.id,
            name: name,
            description: description,
            imageURL: imageURL.isEmpty ? nil : imageURL
        )

        firebaseService.saveCharacter(character)
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                isLoading = false
                if case .failure(let error) = completion {
                    print("Error saving character: \(error.localizedDescription)")
                    errorMessage = error.localizedDescription
                    showError = true
                } else {
                    print("Successfully saved character")
                }
            }, receiveValue: { savedCharacter in
                print("Received saved character with ID: \(savedCharacter.id ?? "unknown")")
                onSave(savedCharacter)
                dismiss()
            })
            .store(in: &cancellables)
    }
}
