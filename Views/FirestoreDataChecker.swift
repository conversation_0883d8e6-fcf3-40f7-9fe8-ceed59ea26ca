//
//  FirestoreDataChecker.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import Combine
import FirebaseFirestore

struct FirestoreDataChecker: View {
    @State private var dramaCount: Int = 0
    @State private var characterCount: Int = 0
    @State private var isLoading: Bool = true
    @State private var errorMessage: String? = nil
    @State private var showAddSampleData: Bool = false
    @State private var cancellables = Set<AnyCancellable>()
    @Environment(\.dismiss) private var dismiss

    init() {}

    var body: some View {
        VStack(spacing: 20) {
            HStack {
                Spacer()
                Text("Firestore Data Checker")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal)

            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                Text("Checking Firestore database...")
                    .font(.headline)
            } else {
                VStack(alignment: .leading, spacing: 15) {
                    HStack {
                        Text("Dramas:")
                            .font(.headline)
                        Text("\(dramaCount)")
                            .font(.title2)
                            .foregroundColor(dramaCount > 0 ? .green : .red)
                    }

                    HStack {
                        Text("Characters:")
                            .font(.headline)
                        Text("\(characterCount)")
                            .font(.title2)
                            .foregroundColor(characterCount > 0 ? .green : .red)
                    }

                    if dramaCount == 0 && characterCount == 0 {
                        Text("No data found in your Firestore database.")
                            .font(.headline)
                            .foregroundColor(.red)
                            .padding(.top)

                        Button("Add Sample Data") {
                            showAddSampleData = true
                        }
                        .buttonStyle(.borderedProminent)
                        .padding(.top)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)
            }

            if let error = errorMessage {
                Text("Error: \(error)")
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
            }

            Spacer()

            VStack(spacing: 12) {
                Button(action: {
                    checkFirestoreData()
                }) {
                    Text("Check Again")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }

                Button(action: {
                    dismiss()
                }) {
                    Text("Close")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.2))
                        .foregroundColor(.primary)
                        .cornerRadius(10)
                }
            }
            .padding(.horizontal)
        }
        .padding()
        .frame(width: 500, height: 400)
        .onAppear {
            checkFirestoreData()
        }
        .sheet(isPresented: $showAddSampleData) {
            SampleDataAdder(onComplete: {
                checkFirestoreData()
            })
        }
    }

    private func checkFirestoreData() {
        isLoading = true
        errorMessage = nil
        print("Checking Firestore database...")

        let db = Firestore.firestore()

        // Check dramas collection
        db.collection("dramas").getDocuments { snapshot, error in
            if let error = error {
                DispatchQueue.main.async {
                    self.errorMessage = "Error checking dramas: \(error.localizedDescription)"
                    self.isLoading = false
                    print("Error checking dramas: \(error.localizedDescription)")
                }
                return
            }

            let dramaCount = snapshot?.documents.count ?? 0
            print("Found \(dramaCount) dramas")

            // Check characters collection
            db.collection("characters").getDocuments { snapshot, error in
                DispatchQueue.main.async {
                    if let error = error {
                        self.errorMessage = "Error checking characters: \(error.localizedDescription)"
                        print("Error checking characters: \(error.localizedDescription)")
                    } else {
                        let characterCount = snapshot?.documents.count ?? 0
                        print("Found \(characterCount) characters")
                        self.dramaCount = dramaCount
                        self.characterCount = characterCount
                    }
                    self.isLoading = false
                }
            }
        }
    }
}
