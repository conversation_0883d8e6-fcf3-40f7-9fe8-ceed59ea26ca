import SwiftUI
import AppKit

struct ImagePickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedImage: NSImage?

    var body: some View {
        VStack(spacing: 20) {
            Text("Select an Image")
                .font(.headline)

            // Simple image picker button
            VStack(spacing: 16) {
                if let image = selectedImage {
                    Image(nsImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 200)
                        .cornerRadius(8)
                } else {
                    Image(systemName: "photo.on.rectangle")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                        .frame(height: 200)
                }

                Button("Choose Image") {
                    openImagePicker()
                }
                .buttonStyle(.bordered)
            }
            .padding()

            HStack {
                Button("Cancel") {
                    selectedImage = nil
                    dismiss()
                }
                .buttonStyle(.bordered)

                Spacer()

                if selectedImage != nil {
                    Button("Use Selected Image") {
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .padding()
        }
        .frame(width: 500, height: 400)
    }

    private func openImagePicker() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = [.image]

        panel.begin { response in
            if response == .OK, let url = panel.url {
                if let image = NSImage(contentsOf: url) {
                    DispatchQueue.main.async {
                        self.selectedImage = image
                    }
                }
            }
        }
    }
}

#Preview {
    ImagePickerView(selectedImage: .constant(nil))
}
