import SwiftUI
import Combine
import FirebaseFirestore

// Helper struct to represent a message group from the same sender
struct MessageGroup: Identifiable {
    var id = UUID()
    let sender: String
    let messages: [Message]
    let isSentMessage: Bool

    // Extract the clean sender name (without the keywords)
    var cleanSenderName: String {
        let name = sender
            .replacingOccurrences(of: "(boyfriend)", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        return name
    }
}

struct MessageBubble: View {
    let message: Message
    let isFirstInGroup: Bool
    let isLastInGroup: Bool
    let showNames: Bool
    @Environment(\.colorScheme) var colorScheme

    // Extract the clean sender name (without the keywords)
    private var cleanSenderName: String {
        let name = message.sender
            .replacingOccurrences(of: "(boyfriend)", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        return name
    }

    // Determine if this is a sent message (right side)
    // We only want messages with the "boyfriend" marker to appear on the right side
    private var isSentMessage: Bool {
        message.sender.lowercased().contains("boyfriend")
    }

    var body: some View {
        HStack {
            if isSentMessage {
                Spacer()
                VStack(alignment: .trailing, spacing: 1) {
                    if showNames && isFirstInGroup {
                        Text(cleanSenderName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.trailing, 8)
                    }

                    Text(message.text)
                        .padding(10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))


                }
            } else {
                VStack(alignment: .leading, spacing: 1) {
                    if showNames && isFirstInGroup {
                        Text(cleanSenderName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.leading, 8)
                    }

                    Text(message.text)
                        .padding(10)
                        .background(colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.2))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .clipShape(RoundedRectangle(cornerRadius: 18, style: .continuous))


                }
                Spacer()
            }
        }
        .padding(.horizontal)
        .padding(.vertical, isFirstInGroup ? 2 : (isLastInGroup ? 2 : 0)) // Reduced padding for tighter bubbles
    }
}

struct MessageGroupView: View {
    let group: MessageGroup
    let showNames: Bool

    var body: some View {
        VStack(spacing: 0) {
            ForEach(Array(group.messages.enumerated()), id: \.element.id) { index, message in
                MessageBubble(
                    message: message,
                    isFirstInGroup: index == 0, // Only first message shows the name
                    isLastInGroup: index == group.messages.count - 1, // Only last message shows the timestamp
                    showNames: showNames
                )
            }
        }
    }
}

struct ChatDetailView: View {
    let chatPreview: ChatPreview
    @StateObject private var viewModel: ChatDetailViewModel
    @ObservedObject private var firebaseService: FirebaseService
    @State private var showingImagePicker = false



    // API Key from UserDefaults
    private var apiKey: String {
        UserDefaults.standard.string(forKey: "gemini_api_key") ?? "AIzaSyAYmQBpmghRwuOJyboIW_sywG9LPDvvUow"
    }

    // Group messages by sender
    private var messageGroups: [MessageGroup] {
        var groups: [MessageGroup] = []
        var currentSender = ""
        var currentMessages: [Message] = []
        var isSentMessage = false

        for message in viewModel.messages {
            // Clean the sender name for comparison
            let cleanSender = message.sender
                .replacingOccurrences(of: "(boyfriend)", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)

            // Check if this is a sent message - only "boyfriend" marker indicates sender side
            let isCurrentMessageSent = message.sender.lowercased().contains("boyfriend")

            // If this is a new sender or the message type changes (sent/received), create a new group
            if cleanSender != currentSender || isCurrentMessageSent != isSentMessage {
                // Save the current group if it's not empty
                if !currentMessages.isEmpty {
                    groups.append(MessageGroup(
                        sender: currentSender,
                        messages: currentMessages,
                        isSentMessage: isSentMessage
                    ))
                }

                // Start a new group
                currentSender = cleanSender
                currentMessages = [message]
                isSentMessage = isCurrentMessageSent
            } else {
                // Add to the current group
                currentMessages.append(message)
            }
        }

        // Add the last group if it's not empty
        if !currentMessages.isEmpty {
            groups.append(MessageGroup(
                sender: currentSender,
                messages: currentMessages,
                isSentMessage: isSentMessage
            ))
        }

        return groups
    }

    // Computed property to determine if we should show sender names
    private var shouldShowNames: Bool {
        // Get unique sender names (excluding the keywords)
        let uniqueSenders = Set(viewModel.messages.map { message in
            message.sender
                .replacingOccurrences(of: "(boyfriend)", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)
        })

        // Show names if there are more than 2 unique senders
        return uniqueSenders.count > 2
    }

    init(chatPreview: ChatPreview, firebaseService: FirebaseService) {
        self.chatPreview = chatPreview
        self.firebaseService = firebaseService
        _viewModel = StateObject(wrappedValue: ChatDetailViewModel(
            chatId: chatPreview.id ?? "",
            firebaseService: firebaseService
        ))
    }

    var body: some View {
        VStack(spacing: 0) {
            // Chat image and upload button
            imageSection

            Divider()

            // Categories header
            categoriesSection

            // Messages
            messagesSection
        }
        .navigationTitle(chatPreview.title)

        .onAppear {
            // Ensure messages are fetched when view appears
            viewModel.fetchMessages()
        }
        .onChange(of: showingImagePicker) { show in
            if show {
                openImagePicker()
            }
        }
        .onChange(of: viewModel.selectedImage) { newImage in
            if newImage != nil {
                viewModel.uploadImage()
            }
        }
        .alert("Error", isPresented: $viewModel.showError) {
            Button("OK") { viewModel.errorMessage = nil }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            } else {
                Text("An unknown error occurred")
            }
        }
    }

    // MARK: - View Components

    private var imageSection: some View {
        VStack {
            if viewModel.isUploading {
                uploadProgressView
            } else if let imageURL = viewModel.chatImageURL, let url = URL(string: imageURL) {
                existingImageView(url: url)
            } else {
                noImageView
            }
        }
        .background(Color.gray.opacity(0.1))
    }

    private var uploadProgressView: some View {
        VStack(spacing: 10) {
            ProgressView(value: viewModel.uploadProgress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle())
                .frame(maxWidth: .infinity)

            Text("Uploading image: \(Int(viewModel.uploadProgress * 100))%")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }

    private func existingImageView(url: URL) -> some View {
        AsyncImage(url: url) { phase in
            switch phase {
            case .empty:
                ProgressView()
                    .frame(height: 200)
            case .success(let image):
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 200)
                    .cornerRadius(8)
                    .overlay(
                        Button(action: {
                            showingImagePicker = true
                        }) {
                            Text("Change Image")
                                .font(.caption)
                                .padding(.horizontal, 10)
                                .padding(.vertical, 5)
                                .background(Color.black.opacity(0.6))
                                .foregroundColor(.white)
                                .cornerRadius(8)
                        }
                        .padding(8),
                        alignment: .bottomTrailing
                    )
            case .failure:
                failedImageView
            @unknown default:
                EmptyView()
            }
        }
        .padding()
    }

    private var failedImageView: some View {
        VStack {
            Image(systemName: "photo.fill")
                .font(.largeTitle)
                .foregroundColor(.gray)
            Text("Failed to load image")
                .font(.caption)
                .foregroundColor(.secondary)
            Button("Upload New Image") {
                showingImagePicker = true
            }
            .buttonStyle(.bordered)
            .padding(.top, 8)
        }
        .frame(height: 150)
    }

    private var noImageView: some View {
        VStack {
            Image(systemName: "photo.fill")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            Text("No image uploaded")
                .font(.headline)
                .foregroundColor(.secondary)
            Button("Upload Image") {
                showingImagePicker = true
            }
            .buttonStyle(.bordered)
            .padding(.top, 8)
        }
        .frame(height: 150)
        .padding()
    }

    private var categoriesSection: some View {
        Group {
            if let categories = chatPreview.categories, !categories.isEmpty {
                VStack(spacing: 0) {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(categories, id: \.self) { category in
                                Text(category)
                                    .font(.caption)
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 5)
                                    .background(Color.blue.opacity(0.2))
                                    .foregroundColor(.blue)
                                    .cornerRadius(12)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                    }
                    .frame(height: 40)
                    .background(Color.gray.opacity(0.1))

                    Divider()
                }
            } else {
                EmptyView()
            }
        }
    }

    private var messagesSection: some View {
        ScrollView {
            LazyVStack {
                if viewModel.messages.isEmpty {
                    ProgressView()
                        .padding()
                } else {
                    ForEach(messageGroups) { group in
                        MessageGroupView(group: group, showNames: shouldShowNames)
                    }
                }
            }
        }
    }

    private func openImagePicker() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = [.image]

        panel.begin { response in
            if response == .OK, let url = panel.url {
                if let image = NSImage(contentsOf: url) {
                    DispatchQueue.main.async {
                        self.viewModel.selectedImage = image
                        self.showingImagePicker = false
                    }
                }
            } else {
                self.showingImagePicker = false
            }
        }
    }

}
