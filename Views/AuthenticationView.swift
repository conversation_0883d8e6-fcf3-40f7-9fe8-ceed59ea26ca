//
//  AuthenticationView.swift
//  Text_Drama_Publisher
//
//  Created by Augment on 12/11/24.
//

import SwiftUI
import Combine
import FirebaseAuth

struct AuthenticationView: View {
    @ObservedObject var firebaseService: FirebaseService
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var isSignUp = false
    @State private var isLoading = false
    @State private var errorMessage = ""
    @State private var showError = false
    @State private var showPasswordReset = false
    @State private var resetEmail = ""
    @State private var showResetSuccess = false
    @State private var cancellables = Set<AnyCancellable>()

    var body: some View {
        VStack(spacing: 20) {
            if firebaseService.isSignedIn {
                signedInView
            } else {
                authenticationForm
            }
        }
        .padding()
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .alert("Password Reset", isPresented: $showPasswordReset) {
            TextField("Email", text: $resetEmail)
            But<PERSON>("Send Reset Email") {
                sendPasswordReset()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Enter your email to receive a password reset link")
        }
        .alert("Success", isPresented: $showResetSuccess) {
            Button("OK") { }
        } message: {
            Text("Password reset email sent successfully")
        }
    }

    private var signedInView: some View {
        VStack(spacing: 16) {
            Text("✅ Signed In")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.green)

            if let user = firebaseService.currentUser {
                VStack(spacing: 4) {
                    Text("Email: \(user.email ?? "Unknown")")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("User ID: \(user.uid)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                        .truncationMode(.middle)
                }
            } else if UserDefaults.standard.bool(forKey: "firebase_auth_success") {
                VStack(spacing: 4) {
                    Text("Auth State: Persisted")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    if let email = UserDefaults.standard.string(forKey: "firebase_user_email") {
                        Text("Email: \(email)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            Text("You can now save chats and upload images")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button("Sign Out") {
                signOut()
            }
            .buttonStyle(.bordered)
            .controlSize(.large)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.green.opacity(0.1))
        )
    }

    private var authenticationForm: some View {
        VStack(spacing: 16) {
            Text(isSignUp ? "Create Account" : "Sign In")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Authentication is required to save chats and upload images")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            VStack(spacing: 12) {
                TextField("Email", text: $email)
                    .textFieldStyle(.roundedBorder)
                    .autocorrectionDisabled()

                SecureField("Password", text: $password)
                    .textFieldStyle(.roundedBorder)

                if isSignUp {
                    SecureField("Confirm Password", text: $confirmPassword)
                        .textFieldStyle(.roundedBorder)
                }
            }

            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
            } else {
                VStack(spacing: 8) {
                    Button(isSignUp ? "Create Account" : "Sign In") {
                        if isSignUp {
                            signUp()
                        } else {
                            signIn()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .disabled(email.isEmpty || password.isEmpty || (isSignUp && confirmPassword.isEmpty))

                    HStack {
                        Button(isSignUp ? "Already have an account? Sign In" : "Don't have an account? Sign Up") {
                            isSignUp.toggle()
                            clearFields()
                        }
                        .buttonStyle(.plain)
                        .font(.caption)

                        if !isSignUp {
                            Button("Forgot Password?") {
                                resetEmail = email
                                showPasswordReset = true
                            }
                            .buttonStyle(.plain)
                            .font(.caption)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.windowBackgroundColor))
        )
    }

    private func signIn() {
        isLoading = true
        errorMessage = ""

        firebaseService.signIn(email: email, password: password)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        // Check if this is actually a successful manual authentication
                        if let nsError = error as NSError?, nsError.code == 200 {
                            // This is a successful manual authentication
                            print("✅ Manual authentication successful")
                            self.clearFields()
                            return
                        }

                        // Handle actual errors
                        if error.localizedDescription.contains("Invalid email") {
                            self.errorMessage = "Please enter a valid email address"
                        } else if error.localizedDescription.contains("keychain") {
                            self.errorMessage = "Keychain error detected. Using alternative authentication..."
                        } else if error.localizedDescription.contains("Firebase Auth Error") {
                            self.errorMessage = error.localizedDescription
                        } else {
                            self.errorMessage = error.localizedDescription
                        }
                        self.showError = true
                    }
                },
                receiveValue: { _ in
                    self.clearFields()
                }
            )
            .store(in: &cancellables)
    }

    private func signUp() {
        guard password == confirmPassword else {
            errorMessage = "Passwords do not match"
            showError = true
            return
        }

        guard password.count >= 6 else {
            errorMessage = "Password must be at least 6 characters"
            showError = true
            return
        }

        isLoading = true
        errorMessage = ""

        firebaseService.signUp(email: email, password: password)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        // Check if this is actually a successful manual authentication
                        if let nsError = error as NSError?, nsError.code == 200 {
                            // This is a successful manual sign-up
                            print("✅ Manual sign-up successful")
                            self.clearFields()
                            return
                        }

                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    }
                },
                receiveValue: { _ in
                    self.clearFields()
                }
            )
            .store(in: &cancellables)
    }

    private func signOut() {
        firebaseService.signOut()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    }
                },
                receiveValue: { _ in
                    // Sign out successful
                }
            )
            .store(in: &cancellables)
    }

    private func sendPasswordReset() {
        guard !resetEmail.isEmpty else { return }

        firebaseService.resetPassword(email: resetEmail)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    } else {
                        self.showResetSuccess = true
                    }
                    self.showPasswordReset = false
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }

    private func clearFields() {
        email = ""
        password = ""
        confirmPassword = ""
    }
}
