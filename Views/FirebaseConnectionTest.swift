//
//  FirebaseConnectionTest.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import Combine
import FirebaseCore

struct FirebaseConnectionTest: View {
    @ObservedObject private var firebaseService = FirebaseService.shared
    @State private var connectionStatus: String = "Testing connection..."
    @State private var isConnected: Bool = false
    @State private var errorMessage: String? = nil
    @State private var projectId: String = ""
    @State private var cancellables = Set<AnyCancellable>()
    @Environment(\.dismiss) private var dismiss

    init() {}

    var body: some View {
        VStack(spacing: 20) {
            HStack {
                Spacer()
                Text("Firebase Connection Test")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
                Button(action: {
                    dismiss()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.gray)
                }
            }
            .padding(.horizontal)

            if isConnected {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
            } else if errorMessage != nil {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)
            } else {
                ProgressView()
                    .scaleEffect(1.5)
            }

            Text(connectionStatus)
                .font(.headline)
                .multilineTextAlignment(.center)
                .padding()

            if let error = errorMessage {
                Text("Error: \(error)")
                    .font(.subheadline)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
            }

            if isConnected {
                Text("Connected to Firebase project: \(projectId)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(spacing: 12) {
                Button(action: {
                    testConnection()
                }) {
                    Text("Test Connection Again")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }

                Button(action: {
                    dismiss()
                }) {
                    Text("Close")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.gray.opacity(0.2))
                        .foregroundColor(.primary)
                        .cornerRadius(10)
                }
            }
            .padding(.horizontal)
        }
        .padding()
        .frame(width: 500, height: 400)
        .onAppear {
            testConnection()
        }
    }

    private func testConnection() {
        connectionStatus = "Testing connection to Firebase..."
        isConnected = false
        errorMessage = nil
        print("Testing connection to Firebase...")

        // Get the project ID from GoogleService-Info.plist
        if let filePath = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
           let plistDict = NSDictionary(contentsOfFile: filePath),
           let projectId = plistDict["PROJECT_ID"] as? String {
            self.projectId = projectId
        } else {
            errorMessage = "Could not read PROJECT_ID from GoogleService-Info.plist"
            connectionStatus = "Connection failed"
            return
        }

        // Use the FirebaseService to test the connection
        firebaseService.testFirebaseConnection { success, error in
            DispatchQueue.main.async {
                if success {
                    self.isConnected = true
                    self.connectionStatus = "Successfully connected to Firebase!"
                    print("Successfully connected to Firebase!")
                } else {
                    self.isConnected = false
                    self.errorMessage = error
                    self.connectionStatus = "Connection failed"
                    print("Connection failed: \(error ?? "Unknown error")")
                }
            }
        }
    }
}
