import SwiftUI
import FirebaseFirestore
import Combine

struct ChatListView: View {
    @StateObject private var viewModel = ChatPreviewViewModel()
    @ObservedObject private var firebaseService = FirebaseService.shared
    @State private var selectedChat: ChatPreview?
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var cancellables = Set<AnyCancellable>()

    var body: some View {
        NavigationSplitView {
            ChatListContent(
                chatPreviews: viewModel.chatPreviews,
                selectedChat: $selectedChat
            )
            .navigationTitle("Text Drama")
            .onAppear {
                print("ChatListView appeared")
            }
            .alert("Error", isPresented: $showError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }

        } detail: {
            ChatDetailContent(selectedChat: selectedChat, firebaseService: firebaseService)
        }
    }
}

// MARK: - Helper Views

struct ChatListContent: View {
    let chatPreviews: [ChatPreview]
    @Binding var selectedChat: ChatPreview?

    var body: some View {
        Group {
            if chatPreviews.isEmpty {
                LoadingView()
            } else {
                ChatPreviewList(
                    chatPreviews: chatPreviews,
                    selectedChat: $selectedChat
                )
            }
        }
    }
}

struct LoadingView: View {
    var body: some View {
        VStack {
            Text("Loading chats...")
                .font(.headline)
            ProgressView()
        }
    }
}

struct ChatPreviewList: View {
    let chatPreviews: [ChatPreview]
    @Binding var selectedChat: ChatPreview?

    var body: some View {
        List(selection: $selectedChat) {
            ForEach(chatPreviews) { chat in
                NavigationLink(value: chat) {
                    ChatPreviewRow(chat: chat)
                }
            }
        }
    }
}

struct ChatPreviewRow: View {
    let chat: ChatPreview

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(chat.title)
                .font(.headline)
                .foregroundColor(.primary)

            Text(chat.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)

            // Categories
            if let categories = chat.categories, !categories.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 6) {
                        ForEach(categories, id: \.self) { category in
                            Text(category)
                                .font(.caption2)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.2))
                                .foregroundColor(.blue)
                                .cornerRadius(10)
                        }
                    }
                }
                .frame(height: 24)
            }

            HStack {
                Image(systemName: "message")
                    .foregroundColor(.blue)
                Text("\(chat.messageCount) messages")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()
            }
        }
        .padding(.vertical, 4)
    }
}

struct ChatDetailContent: View {
    let selectedChat: ChatPreview?
    let firebaseService: FirebaseService

    var body: some View {
        if let selectedChat = selectedChat {
            ChatDetailView(chatPreview: selectedChat, firebaseService: firebaseService)
                // Force view to refresh when selectedChat changes
                .id(selectedChat.id ?? UUID().uuidString)
        } else {
            Text("Select a chat to view details")
                .font(.title2)
                .foregroundColor(.secondary)
        }
    }
}
