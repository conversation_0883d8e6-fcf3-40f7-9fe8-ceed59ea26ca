//
//  AIGenerationView.swift
//  Text_Drama_Publisher
//
//  Created by Augment on 5/16/25.
//

import SwiftUI
import Combine
import FirebaseFirestore
import FirebaseAuth

// Category toggle button component
struct CategoryToggleButton: View {
    let category: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(category)
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? Color.blue : Color.gray.opacity(0.2))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(15)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AIGenerationView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("geminiApiKey") private var apiKey: String = "AIzaSyAYmQBpmghRwuOJyboIW_sywG9LPDvvUow"
    @AppStorage("authorName") private var authorName: String = ""

    // Available categories (genres)
    private let availableCategories = [
        "Romance", "Comedy", "Drama", "Action", "Thriller",
        "Horror", "Fantasy", "Sci-Fi", "Mystery", "Adventure",
        "Crime", "Historical", "Western", "Family", "Animation"
    ]

    @State private var title: String = ""
    @State private var characters: String = ""
    @State private var setting: String = ""
    @State private var style: String = ""
    @State private var additionalPrompt: String = ""
    @State private var startingPoint: String = ""
    @State private var endingPoint: String = ""
    @State private var messageCount: Int = 20
    @State private var selectedCategories: Set<String> = []
    @State private var generatedContent: String = ""
    @State private var isGenerating = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var isEditing = false
    @State private var editedContent: String = ""
    @State private var isSaving = false
    @State private var showSaveSuccess = false
    @State private var isTesting = false
    @State private var apiKeyValid = false
    @State private var apiKeyTested = false

    @State private var cancellables = Set<AnyCancellable>()

    private let firebaseService = FirebaseService.shared

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Authentication Status Banner
                authenticationStatusBanner

                // Parameters section
                VStack(alignment: .leading, spacing: 20) {
                    Text("Drama Parameters")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.bottom, 4)

                    // Title
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Title")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("Enter a title for your drama", text: $title)
                            .textFieldStyle(.roundedBorder)
                            .padding(.bottom, 4)
                    }

                    // Characters
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Characters (comma separated)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("E.g., John, Sarah, Michael", text: $characters)
                            .textFieldStyle(.roundedBorder)
                            .help("Enter character names separated by commas")
                            .padding(.bottom, 4)
                    }

                    // Setting
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Setting")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("E.g., modern day office, fantasy world", text: $setting)
                            .textFieldStyle(.roundedBorder)
                            .help("Describe the setting or context")
                            .padding(.bottom, 4)
                    }

                    // Style
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Style")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("E.g., romantic, comedy, drama, thriller", text: $style)
                            .textFieldStyle(.roundedBorder)
                            .help("Describe the style (e.g., romantic, comedy, drama)")
                            .padding(.bottom, 4)
                    }

                    // Starting Point (Optional)
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Starting Point (Optional)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("E.g., Hey, how was your day?", text: $startingPoint)
                            .textFieldStyle(.roundedBorder)
                            .help("Set how the conversation should begin")
                            .padding(.bottom, 4)
                    }

                    // Ending Point (Optional)
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Ending Point (Optional)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextField("E.g., Let's talk about this tomorrow", text: $endingPoint)
                            .textFieldStyle(.roundedBorder)
                            .help("Set where the conversation should end (for future continuation)")
                            .padding(.bottom, 4)
                    }

                    // Additional Prompt
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Additional Prompt")
                            .font(.headline)
                            .foregroundColor(.primary)
                        TextEditor(text: $additionalPrompt)
                            .frame(minHeight: 100)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                            .padding(.bottom, 8)
                            .help("Add any additional instructions or context for the AI")
                    }

                    // Message Count
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Message Count: \(messageCount)")
                            .font(.headline)
                            .foregroundColor(.primary)
                        HStack {
                            Text("10")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Slider(value: Binding(
                                get: { Double(messageCount) },
                                set: { messageCount = Int($0) }
                            ), in: 10...100, step: 5)
                            Text("100")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Text("Adjust the number of messages to generate")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 8)
                    }

                    // Categories selection
                    VStack(alignment: .leading, spacing: 6) {
                        Text("Categories")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Text("Select categories that best describe this chat")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 4)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(availableCategories, id: \.self) { category in
                                    CategoryToggleButton(
                                        category: category,
                                        isSelected: selectedCategories.contains(category),
                                        action: {
                                            if selectedCategories.contains(category) {
                                                selectedCategories.remove(category)
                                            } else {
                                                selectedCategories.insert(category)
                                            }
                                        }
                                    )
                                }
                            }
                            .padding(.vertical, 8)
                        }
                        .frame(height: 50)
                    }
                    .padding(.vertical, 8)

                    // API Key status indicator
                    HStack {
                        Text("API Key Status:")
                            .font(.headline)
                            .foregroundColor(.primary)

                        if isTesting {
                            ProgressView()
                                .scaleEffect(0.7)
                            Text("Testing...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        } else if apiKeyTested {
                            if apiKeyValid {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                    Text("Valid")
                                        .foregroundColor(.green)
                                }
                            } else {
                                HStack {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.red)
                                    Text("Invalid")
                                        .foregroundColor(.red)
                                }
                            }
                        } else {
                            Text("Not tested")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Button("Test API Key") {
                            testApiKey()
                        }
                        .disabled(isTesting || isGenerating)
                        .buttonStyle(.bordered)
                        .controlSize(.small)
                    }
                    .padding(.top, 8)

                    // Generate button
                    HStack {
                        Spacer()
                        Button(action: generateDrama) {
                            HStack {
                                if isGenerating {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle())
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "wand.and.stars")
                                }
                                Text(isGenerating ? "Generating..." : "Generate Drama")
                            }
                            .frame(minWidth: 180)
                        }
                        .disabled(isGenerating || isTesting)
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        Spacer()
                    }
                    .padding(.top, 16)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color(.windowBackgroundColor))
                )
                .padding(.horizontal)

                // Content section
                VStack {
                    if generatedContent.isEmpty {
                        VStack(spacing: 20) {
                            if isGenerating {
                                VStack(spacing: 20) {
                                    ProgressView()
                                        .scaleEffect(1.5)
                                    Text("Generating content with Gemini AI...")
                                        .font(.headline)
                                }
                                .padding(40)
                            } else if !errorMessage.isEmpty {
                                VStack(spacing: 16) {
                                    Image(systemName: "exclamationmark.triangle")
                                        .font(.system(size: 48))
                                        .foregroundColor(.orange)
                                    Text("Error generating content")
                                        .font(.headline)
                                        .foregroundColor(.orange)
                                    Text(errorMessage)
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal)
                                    Button("Try Again") {
                                        generateDrama()
                                    }
                                    .buttonStyle(.borderedProminent)
                                    .padding(.top, 12)
                                }
                                .padding(40)
                            } else {
                                VStack(spacing: 16) {
                                    Image(systemName: "text.bubble")
                                        .font(.system(size: 48))
                                        .foregroundColor(.secondary)
                                    Text("Generated content will appear here")
                                        .font(.headline)
                                        .foregroundColor(.secondary)
                                }
                                .padding(60)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .frame(minHeight: 300)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(.textBackgroundColor).opacity(0.5))
                        )
                    } else {
                        VStack(spacing: 0) {
                            HStack {
                                Text("Generated Content")
                                    .font(.title2)
                                    .fontWeight(.bold)

                                Spacer()

                                Button(action: {
                                    isEditing.toggle()
                                    if isEditing {
                                        editedContent = generatedContent
                                    } else {
                                        generatedContent = editedContent
                                    }
                                }) {
                                    Text(isEditing ? "Preview" : "Edit")
                                }
                                .buttonStyle(.bordered)

                                Button(action: generateDrama) {
                                    Text("Regenerate")
                                }
                                .buttonStyle(.bordered)
                            }
                            .padding()

                            if isEditing {
                                TextEditor(text: $editedContent)
                                    .font(.body)
                                    .padding([.horizontal, .bottom])
                                    .frame(maxWidth: .infinity)
                                    .frame(minHeight: 400)
                            } else {
                                ScrollView {
                                    Text(generatedContent)
                                        .padding()
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(minHeight: 400)
                                .padding([.horizontal, .bottom])
                            }

                            Divider()

                            HStack {
                                Spacer()

                                Button(action: saveDrama) {
                                    HStack {
                                        if isSaving {
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle())
                                                .scaleEffect(0.8)
                                        } else if firebaseService.isUserAuthenticated() {
                                            Image(systemName: "square.and.arrow.down")
                                        } else {
                                            Image(systemName: "lock.fill")
                                        }

                                        if firebaseService.isUserAuthenticated() {
                                            Text(isSaving ? "Saving..." : "Save to Firebase")
                                        } else {
                                            Text("Sign In Required")
                                        }
                                    }
                                    .frame(minWidth: 180)
                                }
                                .disabled(isSaving || !firebaseService.isUserAuthenticated())
                                .buttonStyle(.borderedProminent)
                                .controlSize(.large)
                                .padding()

                                Spacer()
                            }
                        }
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(.windowBackgroundColor))
                        )
                    }
                }
                .padding(.horizontal)
                .padding(.bottom, 20)
            }
            .padding(.vertical, 20)
            .disabled(isGenerating || isSaving)
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage)
        }
        .alert("Success", isPresented: $showSaveSuccess) {
            Button("OK") { }
        } message: {
            Text("Drama saved successfully!")
        }
        .onAppear {
            // Test the API key when the view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                testApiKey()
            }
        }
    }

    // MARK: - Authentication Status Banner
    private var authenticationStatusBanner: some View {
        HStack {
            if firebaseService.isSignedIn || firebaseService.isUserAuthenticated() {
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("✅ Authenticated")
                            .font(.headline)
                            .foregroundColor(.green)

                        if let user = firebaseService.currentUser {
                            Text("Signed in as: \(user.email ?? "Unknown")")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            Text("Ready to save chats")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    Text("You can save generated chats")
                        .font(.caption)
                        .foregroundColor(.green)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(6)
                }
            } else {
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("⚠️ Not Authenticated")
                            .font(.headline)
                            .foregroundColor(.orange)

                        Text("Sign in to save generated chats")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Text("Go to Settings → Authentication")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.orange.opacity(0.1))
                        .cornerRadius(6)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(firebaseService.isUserAuthenticated() ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
                .stroke(firebaseService.isUserAuthenticated() ? Color.green.opacity(0.3) : Color.orange.opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal)
    }

    private func testApiKey() {
        // Clear previous errors
        errorMessage = ""
        showError = false
        isTesting = true
        apiKeyTested = false
        apiKeyValid = false

        print("🔑 Testing Gemini API key: \(apiKey.prefix(5))...[REDACTED]")

        let geminiService = GeminiService(apiKey: apiKey)

        // Cancel any existing subscriptions
        cancellables.removeAll()

        geminiService.testConnection()
            .receive(on: DispatchQueue.main)
            .sink(receiveCompletion: { completion in
                isTesting = false
                apiKeyTested = true

                switch completion {
                case .finished:
                    print("✅ API key test successful")
                    apiKeyValid = true
                case .failure(let error):
                    print("❌ API key test failed: \(error.localizedDescription)")
                    apiKeyValid = false
                    errorMessage = "API key test failed: \(error.localizedDescription)"
                    showError = true
                }
            }, receiveValue: { _ in })
            .store(in: &cancellables)
    }

    private func generateDrama() {
        // Clear previous content and errors
        errorMessage = ""
        showError = false
        isGenerating = true

        print("🎬 Starting drama generation with Gemini API")
        print("🔑 API Key: \(apiKey.prefix(5))...[REDACTED]")

        let geminiService = GeminiService(apiKey: apiKey)

        // Process input parameters
        let charactersArray = characters.split(separator: ",").map { String($0.trimmingCharacters(in: .whitespacesAndNewlines)) }

        let finalTitle = title.isEmpty ? nil : title
        let finalCharacters = charactersArray.isEmpty ? nil : charactersArray
        let finalSetting = setting.isEmpty ? nil : setting
        let finalStyle = style.isEmpty ? nil : style
        let finalAdditionalPrompt = additionalPrompt.isEmpty ? nil : additionalPrompt
        let finalStartingPoint = startingPoint.isEmpty ? nil : startingPoint
        let finalEndingPoint = endingPoint.isEmpty ? nil : endingPoint

        print("📋 Generation parameters:")
        print("   - Title: \(finalTitle ?? "Not specified")")
        print("   - Characters: \(finalCharacters?.joined(separator: ", ") ?? "Not specified")")
        print("   - Setting: \(finalSetting ?? "Not specified")")
        print("   - Style: \(finalStyle ?? "Not specified")")
        print("   - Starting Point: \(finalStartingPoint ?? "Not specified")")
        print("   - Ending Point: \(finalEndingPoint ?? "Not specified")")
        print("   - Additional Prompt: \(finalAdditionalPrompt ?? "Not specified")")
        print("   - Message Count: \(messageCount)")

        // Cancel any existing subscriptions
        cancellables.removeAll()

        // Set up a timeout timer - longer for larger message counts
        let timeoutSeconds = messageCount > 50 ? 90.0 : 60.0
        let timeoutTimer = Timer.publish(every: timeoutSeconds, on: .main, in: .common).autoconnect()

        timeoutTimer.sink { _ in
            if self.isGenerating {
                print("⏰ Request timed out after \(timeoutSeconds) seconds")
                self.isGenerating = false
                self.errorMessage = "Request timed out after \(Int(timeoutSeconds)) seconds. The API may be experiencing issues or your API key might not have sufficient permissions."
                self.showError = true
                self.cancellables.removeAll() // Cancel any pending requests
            }
        }
        .store(in: &cancellables)

        geminiService.generateDrama(
            title: finalTitle,
            characters: finalCharacters,
            setting: finalSetting,
            style: finalStyle,
            startingPoint: finalStartingPoint,
            endingPoint: finalEndingPoint,
            additionalPrompt: finalAdditionalPrompt,
            messageCount: messageCount
        )
        .receive(on: DispatchQueue.main)
        .sink(receiveCompletion: { completion in
            isGenerating = false

            switch completion {
            case .finished:
                print("✅ Drama generation completed successfully")
            case .failure(let error):
                print("❌ Drama generation failed: \(error.localizedDescription)")
                errorMessage = "Failed to generate drama: \(error.localizedDescription)"
                showError = true
            }
        }, receiveValue: { content in
            print("📝 Received generated content of length: \(content.count) characters")
            generatedContent = content
            editedContent = content
        })
        .store(in: &cancellables)
    }

    private func saveDrama() {
        print("🚀 Starting saveDrama() function")
        isSaving = true

        // Parse the content to create messages
        let content = isEditing ? editedContent : generatedContent
        print("📝 Content to parse (first 200 chars): \(String(content.prefix(200)))")

        let messages = parseMessagesFromContent(content)
        print("📨 Parsed \(messages.count) messages from content")

        // Debug: Print first few messages
        for (index, message) in messages.prefix(3).enumerated() {
            print("📨 Message \(index + 1): \(message.sender) -> \(String(message.text.prefix(50)))")
        }

        // Create a base chat preview with a temporary description
        var chatPreview = createChatPreview(title: title, messages: messages)
        print("📋 Created chat preview with title: \(chatPreview["title"] ?? "Unknown")")

        // Create a GeminiService instance
        let geminiService = GeminiService(apiKey: apiKey)

        // Convert selected categories to array
        let categoriesArray = Array(selectedCategories)
        print("🏷️ Categories: \(categoriesArray)")

        // For now, skip AI description generation and use fallback to isolate the issue
        print("🎯 Skipping AI description generation for debugging, using fallback...")

        let fallbackDescription = createFallbackDescription(
            style: style,
            setting: setting,
            categories: categoriesArray
        )
        print("📝 Using fallback description: \(fallbackDescription)")

        // Update the description in the chat preview
        chatPreview["description"] = fallbackDescription

        // Save to Firebase with the fallback description
        saveToFirebase(chatPreview: chatPreview, messages: messages)
    }

    private func parseMessagesFromContent(_ content: String) -> [Message] {
        print("🔍 Starting parseMessagesFromContent")
        print("📝 Content length: \(content.count) characters")
        print("📝 Content preview: \(String(content.prefix(300)))")

        var messages: [Message] = []

        // Split by lines
        let lines = content.split(separator: "\n")
        print("📄 Split content into \(lines.count) lines")

        var currentSender = ""
        var currentMessage = ""

        // Extract unique character names from the content
        var characterNames = Set<String>()
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if let colonRange = trimmedLine.range(of: ":") {
                let senderEndIndex = colonRange.lowerBound
                let sender = String(trimmedLine[..<senderEndIndex]).trimmingCharacters(in: .whitespacesAndNewlines)
                if !sender.isEmpty {
                    characterNames.insert(sender)
                }
            }
        }

        print("👥 Found character names: \(Array(characterNames))")

        // Determine which characters should appear on the right side
        let characterList = Array(characterNames)
        var rightSideCharacters = Set<String>()

        // If we have at least two characters, designate ONE character to appear on the right side
        if characterList.count >= 2 {
            // Only one character should appear on the right side (sender side)
            // We'll choose the first character in the list
            rightSideCharacters.insert(characterList[0])

            print("👥 Character that will appear on the right side (sender): \(rightSideCharacters.joined(separator: ", "))")
        }

        // Process the messages
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmedLine.isEmpty { continue }

            // Check if this line starts with a name (sender)
            if let colonRange = trimmedLine.range(of: ":") {
                // If we have a previous message, add it
                if !currentSender.isEmpty && !currentMessage.isEmpty {
                    // Modify the sender name for the one character that should appear on the right
                    var finalSender = currentSender
                    if rightSideCharacters.contains(currentSender) {
                        // Append a keyword that will trigger the right-side display
                        // We use a consistent marker (boyfriend) for the right side character
                        finalSender = "\(currentSender) (boyfriend)"
                    }

                    let message = Message(
                        id: UUID().uuidString,
                        sender: finalSender,
                        text: currentMessage,
                        timestamp: Timestamp(date: Date())
                    )
                    messages.append(message)
                }

                // Start a new message
                let senderEndIndex = colonRange.lowerBound
                currentSender = String(trimmedLine[..<senderEndIndex]).trimmingCharacters(in: .whitespacesAndNewlines)

                let messageStartIndex = colonRange.upperBound
                currentMessage = String(trimmedLine[messageStartIndex...]).trimmingCharacters(in: .whitespacesAndNewlines)
            } else {
                // Continue the current message
                if !currentMessage.isEmpty {
                    currentMessage += "\n"
                }
                currentMessage += trimmedLine
            }
        }

        // Add the last message if there is one
        if !currentSender.isEmpty && !currentMessage.isEmpty {
            // Modify the sender name for the one character that should appear on the right
            var finalSender = currentSender
            if rightSideCharacters.contains(currentSender) {
                // Append a keyword that will trigger the right-side display
                // We use a consistent marker (boyfriend) for the right side character
                finalSender = "\(currentSender) (boyfriend)"
            }

            let message = Message(
                id: UUID().uuidString,
                sender: finalSender,
                text: currentMessage,
                timestamp: Timestamp(date: Date())
            )
            messages.append(message)
            print("📨 Added final message: \(finalSender) -> \(String(currentMessage.prefix(50)))")
        }

        print("✅ Parsing complete. Total messages created: \(messages.count)")
        return messages
    }

    private func createChatPreview(title: String, messages: [Message]) -> [String: Any] {
        let finalTitle = title.isEmpty ? "AI Generated Drama" : title

        // Create a document ID from the title (will be replaced in saveToFirebase if needed)
        let documentId = createDocumentId(from: finalTitle)

        // Convert selected categories to array
        let categoriesArray = Array(selectedCategories)

        // If no categories are selected, use the style as a default category if available
        let finalCategories: [String] = categoriesArray.isEmpty && !style.isEmpty ?
            [style.trimmingCharacters(in: .whitespacesAndNewlines)] :
            categoriesArray

        // Use a temporary description - this will be replaced by the AI-generated one
        // before saving to Firebase
        let temporaryDescription = "Generating catchy description..."

        return [
            "title": finalTitle,
            "description": temporaryDescription, // Will be replaced with AI-generated description
            "timestamp": Timestamp(date: Date()),
            "messageCount": messages.count,
            "author": authorName.isEmpty ? "AI Author" : authorName,
            "id": documentId,
            "categories": finalCategories,
            "image_url": nil as String? // Initialize with nil, can be set later
        ]
    }

    // This function will be used as a fallback if AI generation fails
    private func createFallbackDescription(style: String, setting: String, categories: [String]) -> String {
        // Default catchy descriptions if we don't have specific information
        let defaultDescriptions = [
            "Conversations so real you'll forget they're fictional",
            "Authentic exchanges that capture the essence of human connection",
            "Compelling dialogue that brings characters vividly to life",
            "Text conversations that feel pulled straight from real life",
            "Captivating exchanges that reveal the complexity of relationships"
        ]

        // If we have a style, use it
        if !style.isEmpty {
            return "Captivating \(style) conversations that feel incredibly real"
        }

        // If we have a setting but no style
        if !setting.isEmpty {
            return "Authentic conversations set in \(setting) that pull you right into the scene"
        }

        // If we have categories but no style or setting
        if !categories.isEmpty {
            let category = categories.first ?? ""
            return "Engaging \(category) dialogue that captures real human connection"
        }

        return defaultDescriptions.randomElement() ?? "Conversations so real you'll forget they're fictional"
    }

    private func saveToFirebase(chatPreview: [String: Any], messages: [Message]) {
        print("💾 Starting saveToFirebase with \(messages.count) messages")

        // Check if user is authenticated for write operations
        guard FirebaseService.shared.isUserAuthenticated() else {
            DispatchQueue.main.async {
                self.isSaving = false
                self.errorMessage = "Authentication required to save chats. Please sign in from Settings."
                self.showError = true
            }
            return
        }

        // Ensure Firebase Auth session for database operations
        FirebaseService.shared.ensureFirebaseAuthForDatabase { hasFirebaseAuth in
            DispatchQueue.main.async {
                if hasFirebaseAuth {
                    self.performSave(chatPreview: chatPreview, messages: messages)
                } else {
                    self.isSaving = false
                    self.errorMessage = """
                    Production Authentication Setup Required

                    Your app is using production-level authentication but needs updated Firestore rules.

                    OPTION 1 - Enable Firebase Authentication (Recommended):
                    1. Go to Firebase Console → Authentication → Sign-in method
                    2. Enable "Email/Password" authentication
                    3. Enable "Identity Toolkit API" in Google Cloud Console
                    4. Restart the app and try signing in again

                    OPTION 2 - Update Firestore Rules (Quick Fix):
                    1. Go to Firebase Console → Firestore Database → Rules
                    2. Replace with: allow read, write: if true;
                    3. Click Publish

                    See firestore-production-rules.txt for detailed rule options.
                    """
                    self.showError = true
                }
            }
        }
    }

    private func performSave(chatPreview: [String: Any], messages: [Message]) {
        let db = Firestore.firestore()

        // Create a document ID based on the title
        let titleStr = chatPreview["title"] as? String ?? "AI Generated Drama"
        let documentId = createDocumentId(from: titleStr)

        print("📝 Saving drama with title: \(titleStr)")
        print("🆔 Using document ID: \(documentId)")
        print("📊 Chat preview data: \(chatPreview)")

        // Create a new document in the chatPreviews collection with the custom ID
        let chatRef = db.collection("chatPreviews").document(documentId)

        // Add the ID to the chat preview data
        var updatedChatPreview = chatPreview
        updatedChatPreview["id"] = documentId

        // Check if document already exists
        print("🔍 Checking if document exists...")
        chatRef.getDocument { (document, error) in
            if let error = error {
                print("❌ Error checking document existence: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isSaving = false
                    self.errorMessage = "Failed to check document existence: \(error.localizedDescription)"
                    self.showError = true
                }
                return
            }

            if let document = document, document.exists {
                // Document with this ID already exists, append a unique identifier
                let uniqueId = documentId + "-" + UUID().uuidString.prefix(6).lowercased()
                print("⚠️ Document ID already exists, using unique ID instead: \(uniqueId)")

                let uniqueChatRef = db.collection("chatPreviews").document(uniqueId)
                updatedChatPreview["id"] = uniqueId

                self.saveDocument(chatRef: uniqueChatRef, chatPreview: updatedChatPreview, messages: messages)
            } else {
                print("✅ Document doesn't exist, proceeding with original ID")
                // Document doesn't exist, proceed with the original ID
                self.saveDocument(chatRef: chatRef, chatPreview: updatedChatPreview, messages: messages)
            }
        }
    }

    private func createDocumentId(from title: String) -> String {
        // Convert title to lowercase
        let lowercaseTitle = title.lowercased()

        // Replace spaces with hyphens and remove special characters
        let allowedCharacters = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "-"))
        let processedTitle = lowercaseTitle
            .components(separatedBy: CharacterSet.whitespaces)
            .joined(separator: "-")
            .components(separatedBy: allowedCharacters.inverted)
            .joined(separator: "")

        // Ensure the ID is not empty
        return processedTitle.isEmpty ? "ai-generated-drama" : processedTitle
    }

    private func saveDocument(chatRef: DocumentReference, chatPreview: [String: Any], messages: [Message]) {
        print("📄 Starting saveDocument with document ID: \(chatRef.documentID)")
        print("📊 Chat preview to save: \(chatPreview)")
        print("📨 Number of messages to save: \(messages.count)")

        chatRef.setData(chatPreview) { error in
            if let error = error {
                print("❌ Failed to save chat preview: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isSaving = false
                    self.errorMessage = "Failed to save chat preview: \(error.localizedDescription)"
                    self.showError = true
                }
                return
            }

            print("✅ Chat preview saved successfully")
            print("💬 Now saving \(messages.count) messages...")

            // Now save all messages
            let batch = Firestore.firestore().batch()

            for (index, message) in messages.enumerated() {
                let messageRef = Firestore.firestore().collection("dramas").document(chatRef.documentID).collection("messages").document()

                print("📨 Preparing message \(index + 1): \(message.sender) -> \(String(message.text.prefix(30)))")

                let messageData: [String: Any] = [
                    "sender": message.sender,
                    "text": message.text,
                    "timestamp": message.timestamp
                ]

                print("📊 Message data: \(messageData)")

                batch.setData(messageData, forDocument: messageRef)
            }

            print("🔄 Committing batch with \(messages.count) messages...")

            // Commit the batch
            batch.commit { error in
                DispatchQueue.main.async {
                    self.isSaving = false

                    if let error = error {
                        print("❌ Failed to save messages: \(error.localizedDescription)")
                        self.errorMessage = "Failed to save messages: \(error.localizedDescription)"
                        self.showError = true
                    } else {
                        print("✅ All messages saved successfully!")
                        print("🎉 Drama saved to Firebase with ID: \(chatRef.documentID)")
                        self.showSaveSuccess = true
                    }
                }
            }
        }
    }
}
