//
//  SettingsView.swift
//  Text_Drama_Publisher
//
//  Created by Cascade on 5/16/25.
//

import SwiftUI
import FirebaseCore

struct SettingsView: View {
    @AppStorage("projectId") private var projectId: String = ""
    @AppStorage("authorName") private var authorName: String = ""
    @AppStorage("geminiApiKey") private var geminiApiKey: String = "AIzaSyAYmQBpmghRwuOJyboIW_sywG9LPDvvUow"
    @State private var showConnectionTest = false
    @State private var showApiKeyInfo = false
    @ObservedObject private var firebaseService = FirebaseService.shared

    init() {
        // Load project ID from GoogleService-Info.plist
        if let filePath = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
           let plistDict = NSDictionary(contentsOfFile: filePath),
           let projectId = plistDict["PROJECT_ID"] as? String {
            UserDefaults.standard.set(projectId, forKey: "projectId")
        }
    }

    var body: some View {
        Form {
            Section(header: Text("Firebase Configuration")) {
                Text("This app is connected to the Firebase 'Text Drama' project.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("Project ID", text: $projectId)
                    .textFieldStyle(.roundedBorder)
                    .disabled(true)

                Button("Test Firebase Connection") {
                    showConnectionTest = true
                }
                .buttonStyle(.borderedProminent)
            }

            Section(header: Text("Author Information")) {
                TextField("Default Author Name", text: $authorName)
                    .textFieldStyle(.roundedBorder)

                Text("This name will be used as the default author when creating new dramas.")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Section(header: Text("Authentication")) {
                AuthenticationView(firebaseService: firebaseService)

                // Debug info for authentication state
                VStack(alignment: .leading, spacing: 4) {
                    Text("Authentication Debug Info:")
                        .font(.caption)
                        .fontWeight(.semibold)

                    Text(firebaseService.getAuthenticationStatus())
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    Button("Refresh Auth Status") {
                        // Force refresh by checking auth state
                        print(firebaseService.getAuthenticationStatus())
                    }
                    .buttonStyle(.plain)
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .padding(8)
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)
            }

            Section(header: Text("AI Configuration")) {
                TextField("Gemini API Key", text: $geminiApiKey)
                    .textFieldStyle(.roundedBorder)
                    .autocorrectionDisabled()

                Text("This API key is used for generating text dramas with Google's Gemini AI.")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Button("What is this?") {
                    showApiKeyInfo = true
                }
                .buttonStyle(.plain)
                .font(.caption)
                .foregroundColor(.blue)
                .alert("About Gemini API", isPresented: $showApiKeyInfo) {
                    Button("OK", role: .cancel) { }
                } message: {
                    Text("The Gemini API key allows this app to generate text dramas using Google's Gemini AI model. A default key is provided, but you can replace it with your own if needed.")
                }
            }

            Section(header: Text("About")) {
                VStack(alignment: .leading, spacing: 10) {
                    Text("Text Drama Publisher")
                        .font(.headline)

                    Text("Version 1.0")
                        .font(.subheadline)

                    Text("This application is designed to manage and edit content for the Text Drama project. It connects to Firebase to allow you to create, edit, and manage dramas and characters.")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
            }
        }
        .navigationTitle("Settings")
        .padding()
        .frame(maxWidth: 600)
        .sheet(isPresented: $showConnectionTest) {
            FirebaseConnectionTest()
        }
    }
}
