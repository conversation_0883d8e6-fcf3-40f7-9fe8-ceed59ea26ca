# Production-Ready Firebase Security Rules

## The Problem
Your current rules are completely open, allowing anyone to read/write your database.

## The Solution
Use authentication-based rules that only allow authenticated users to access their own data.

## Step 1: Update Firestore Rules

Go to Firebase Console > Firestore Database > Rules and replace with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read/write chat previews
    match /chatPreviews/{chatId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow authenticated users to read/write dramas and their messages
    match /dramas/{dramaId} {
      allow read, write: if request.auth != null;
      
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Allow authenticated users to read/write characters
    match /characters/{characterId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Step 2: Update Storage Rules (for images)

Go to Firebase Console > Storage > Rules and replace with:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to read all images
    match /{allPaths=**} {
      allow read: if request.auth != null;
    }
    
    // Allow authenticated users to upload images
    match /public_images/{imageId} {
      allow write: if request.auth != null;
    }
    
    // Allow authenticated users to upload chat images
    match /chat_images/{imageId} {
      allow write: if request.auth != null;
    }
  }
}
```

## Step 3: Enable Anonymous Authentication

Since your app doesn't have a login system, enable anonymous authentication:

1. Go to Firebase Console > Authentication > Sign-in method
2. Click on "Anonymous" 
3. Toggle "Enable" to ON
4. Click "Save"

## Step 4: Update Your App Code

The app needs to authenticate users automatically. I'll provide the code changes needed.

## Benefits of These Rules:
- ✅ Only authenticated users can access data
- ✅ Users can only access their own user data
- ✅ Shared data (chats, dramas) is accessible to all authenticated users
- ✅ Anonymous authentication means no login required for users
- ✅ Much more secure than public rules
- ✅ Still allows your app to function normally

## How It Works:
1. When users open your app, they're automatically signed in anonymously
2. Each user gets a unique anonymous ID
3. The rules check that the user is authenticated before allowing access
4. Users can access shared content (chats, dramas) but not other users' private data
