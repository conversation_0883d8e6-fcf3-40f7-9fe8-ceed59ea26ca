# Firebase Rules: Read Public, Write Authenticated

## 🎯 Perfect Setup: Read Public, Write Authenticated

Your app now has the ideal security setup:
- ✅ **Anyone can READ** data (no authentication required)
- ✅ **Only authenticated users can WRITE** data
- ✅ **Sign-in available in Settings** for users who want to create content

## Firebase Rules to Apply

### 1. Firestore Database Rules

Go to **Firebase Console → Firestore Database → Rules** and use:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow public read access to all documents
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    // Specific rules for better organization
    match /chatPreviews/{chatId} {
      allow read: if true;
      allow write: if request.auth != null;
    }

    match /dramas/{dramaId} {
      allow read: if true;
      allow write: if request.auth != null;

      match /messages/{messageId} {
        allow read: if true;
        allow write: if request.auth != null;
      }
    }

    match /characters/{characterId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 2. Storage Rules

Go to **Firebase Console → Storage → Rules** and use:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow public read access to all images
    match /{allPaths=**} {
      allow read: if true;
    }

    // Only authenticated users can upload images
    match /public_images/{imageId} {
      allow write: if request.auth != null
                   && request.resource.size < 10 * 1024 * 1024  // 10MB limit
                   && request.resource.contentType.matches('image/.*');
    }

    match /chat_images/{imageId} {
      allow write: if request.auth != null
                   && request.resource.size < 10 * 1024 * 1024  // 10MB limit
                   && request.resource.contentType.matches('image/.*');
    }
  }
}
```

## Step-by-Step Instructions:

### For Firestore:
1. Go to https://console.firebase.google.com/
2. Select your **text-drama** project
3. Click **Firestore Database** → **Rules**
4. Delete all existing rules
5. Paste the Firestore rules above
6. Click **Publish**

### For Storage:
1. In the same Firebase Console
2. Click **Storage** → **Rules**
3. Delete all existing rules
4. Paste the Storage rules above
5. Click **Publish**

### Enable Authentication Methods:
1. In Firebase Console, click **Authentication** → **Sign-in method**
2. Click **Email/Password**
3. Toggle **Enable** to ON
4. Click **Save**
5. Click **Anonymous**
6. Toggle **Enable** to ON (this is used as fallback for keychain issues)
7. Click **Save**

## What Your App Can Do Now:

### 👥 **For All Users (No Sign-in Required):**
- ✅ View all existing chats and dramas
- ✅ Browse all content
- ✅ View all images
- ✅ Read everything publicly

### 🔐 **For Authenticated Users Only:**
- ✅ Save new AI-generated chats
- ✅ Upload images to chats
- ✅ Create and modify content
- ✅ All write operations

### 🎛️ **Authentication Features:**
- ✅ Sign in/Sign up in Settings
- ✅ Password reset functionality
- ✅ Automatic sign-out option
- ✅ Clear authentication status display

## Benefits:
🌍 **Public Access**: Anyone can enjoy your content
🔒 **Secure Writing**: Only authenticated users can create/modify
📱 **User-Friendly**: Optional authentication for creators
🚀 **Scalable**: Perfect for content platforms
💡 **Smart**: Encourages engagement while protecting data

Your app now has the perfect balance of accessibility and security!
