//
//  AppDelegate.swift
//  Text_Drama_Publisher
//
//  Created by <PERSON> on 5/16/25.
//

import Cocoa
import SwiftUI
import FirebaseCore
import FirebaseAuth

class AppDelegate: NSObject, NSApplicationDelegate {
    var window: NSWindow!

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Configure Firebase with error handling
        setupFirebase()

        // Create the SwiftUI view that provides the window contents.
        let contentView = ContentView()

        // Create the window and set the content view.
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 800, height: 600),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false)
        window.center()
        window.setFrameAutosaveName("Main Window")
        window.contentView = NSHostingView(rootView: contentView)
        window.makeKeyAndOrderFront(nil)
    }

    private func setupFirebase() {
        // Check if Firebase is already configured
        if FirebaseApp.app() == nil {
            do {
                // Verify GoogleService-Info.plist exists
                guard let filePath = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") else {
                    print("ERROR: GoogleService-Info.plist not found!")
                    return
                }

                // Load and verify the plist contents
                guard let plistDict = NSDictionary(contentsOfFile: filePath),
                      let bundleID = plistDict["BUNDLE_ID"] as? String,
                      let projectID = plistDict["PROJECT_ID"] as? String else {
                    print("ERROR: GoogleService-Info.plist is missing required keys!")
                    return
                }

                // Print configuration info for debugging
                print("Configuring Firebase with:")
                print("  Bundle ID: \(bundleID)")
                print("  Project ID: \(projectID)")

                // Configure Firebase
                FirebaseApp.configure()

                // Verify configuration was successful
                if let options = FirebaseApp.app()?.options {
                    print("Firebase successfully configured with project: \(options.projectID ?? "unknown")")
                } else {
                    print("WARNING: Firebase configuration may not have been successful")
                }
            } catch {
                print("ERROR setting up Firebase: \(error.localizedDescription)")
            }
        } else {
            print("Firebase already configured")
        }
    }

    func applicationWillTerminate(_ notification: Notification) {
        // Insert code here to tear down your application
    }
}
